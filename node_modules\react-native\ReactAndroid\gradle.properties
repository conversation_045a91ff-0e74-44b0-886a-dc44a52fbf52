VERSION_NAME=0.79.2
react.internal.publishingGroup=com.facebook.react

android.useAndroidX=true

# We want to have more fine grained control on the Java version for
# ReactAndroid, therefore we disable RGNP Java version alignment mechanism
react.internal.disableJavaVersionAlignment=true

# Binary Compatibility Validator properties

# We ignore:
# - BuildConfig classes because they are generated and not part of the public API
binaryCompatibilityValidator.ignoredClasses=com.facebook.react.BuildConfig,\
  com.facebook.react.views.progressbar.ReactProgressBarViewManager$$PropsSetter,\
  com.facebook.react.views.progressbar.ProgressBarShadowNode$$PropsSetter

binaryCompatibilityValidator.ignoredPackages=com.facebook.debug,\
  com.facebook.fbreact,\
  com.facebook.hermes,\
  com.facebook.perftest,\
  com.facebook.proguard,\
  com.facebook.react.bridgeless.internal,\
  com.facebook.react.common.annotations,\
  com.facebook.react.fabric.internal.interop,\
  com.facebook.react.flipper,\
  com.facebook.react.internal,\
  com.facebook.react.module.processing,\
  com.facebook.react.processing,\
  com.facebook.react.runtime.internal,\
  com.facebook.react.views.text.internal,\
  com.facebook.systrace,\
  com.facebook.yoga
binaryCompatibilityValidator.nonPublicMarkers=com.facebook.react.common.annotations.VisibleForTesting,\
  com.facebook.react.common.annotations.UnstableReactNativeAPI
binaryCompatibilityValidator.validationDisabled=true
binaryCompatibilityValidator.outputApiFileName=ReactAndroid
