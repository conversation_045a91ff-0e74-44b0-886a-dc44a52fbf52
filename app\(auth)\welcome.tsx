// app/(auth)/welcome.tsx

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';

import { useTheme } from '@/hooks/useTheme';
import { Spacing, FontSizes, FontWeights, BorderRadius } from '@/constants/Colors';

const { width, height } = Dimensions.get('window');

export default function WelcomeScreen() {
  const { colors } = useTheme();

  const handleLogin = () => {
    router.push('/(auth)/login');
  };

  const handleRegister = () => {
    router.push('/(auth)/register');
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    content: {
      flex: 1,
      padding: Spacing.xl,
      justifyContent: 'center',
      alignItems: 'center',
    },
    logoContainer: {
      alignItems: 'center',
      marginBottom: Spacing.xxl,
    },
    logo: {
      width: 120,
      height: 120,
      borderRadius: BorderRadius.xl,
      backgroundColor: colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: Spacing.lg,
    },
    logoText: {
      fontSize: FontSizes.xxxl,
      fontWeight: FontWeights.bold,
      color: '#FFFFFF',
    },
    title: {
      fontSize: FontSizes.xxxl,
      fontWeight: FontWeights.bold,
      color: colors.text,
      textAlign: 'center',
      marginBottom: Spacing.sm,
    },
    subtitle: {
      fontSize: FontSizes.lg,
      color: colors.textSecondary,
      textAlign: 'center',
      marginBottom: Spacing.xxl,
      lineHeight: FontSizes.lg * 1.4,
    },
    featuresContainer: {
      width: '100%',
      marginBottom: Spacing.xxl,
    },
    feature: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: Spacing.md,
      paddingHorizontal: Spacing.lg,
    },
    featureIcon: {
      width: 24,
      height: 24,
      borderRadius: BorderRadius.round,
      backgroundColor: colors.primary,
      marginRight: Spacing.md,
    },
    featureText: {
      fontSize: FontSizes.md,
      color: colors.text,
      flex: 1,
    },
    buttonsContainer: {
      width: '100%',
      paddingHorizontal: Spacing.lg,
    },
    loginButton: {
      backgroundColor: colors.primary,
      paddingVertical: Spacing.lg,
      paddingHorizontal: Spacing.xl,
      borderRadius: BorderRadius.lg,
      alignItems: 'center',
      marginBottom: Spacing.md,
    },
    loginButtonText: {
      fontSize: FontSizes.lg,
      fontWeight: FontWeights.semibold,
      color: '#FFFFFF',
    },
    registerButton: {
      backgroundColor: 'transparent',
      paddingVertical: Spacing.lg,
      paddingHorizontal: Spacing.xl,
      borderRadius: BorderRadius.lg,
      borderWidth: 2,
      borderColor: colors.primary,
      alignItems: 'center',
    },
    registerButtonText: {
      fontSize: FontSizes.lg,
      fontWeight: FontWeights.semibold,
      color: colors.primary,
    },
    footer: {
      padding: Spacing.lg,
      alignItems: 'center',
    },
    footerText: {
      fontSize: FontSizes.sm,
      color: colors.textSecondary,
      textAlign: 'center',
    },
  });

  const features = [
    'Create professional estimates quickly',
    'Manage clients and project details',
    'Use templates for common projects',
    'Track estimate status and approvals',
    'Generate reports and analytics',
  ];

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.logoContainer}>
          <View style={styles.logo}>
            <Text style={styles.logoText}>CE</Text>
          </View>
          <Text style={styles.title}>Construction{'\n'}Estimator</Text>
          <Text style={styles.subtitle}>
            Professional estimate management{'\n'}for construction professionals
          </Text>
        </View>

        <View style={styles.featuresContainer}>
          {features.map((feature, index) => (
            <View key={index} style={styles.feature}>
              <View style={styles.featureIcon} />
              <Text style={styles.featureText}>{feature}</Text>
            </View>
          ))}
        </View>

        <View style={styles.buttonsContainer}>
          <TouchableOpacity style={styles.loginButton} onPress={handleLogin}>
            <Text style={styles.loginButtonText}>Sign In</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.registerButton} onPress={handleRegister}>
            <Text style={styles.registerButtonText}>Create Account</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Streamline your construction business{'\n'}with professional estimates
        </Text>
      </View>
    </SafeAreaView>
  );
}
