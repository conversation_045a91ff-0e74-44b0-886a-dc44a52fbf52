// components/ui/Input.tsx

import React, { useState } from 'react';
import {
    StyleSheet,
    Text,
    TextInput,
    TextInputProps,
    TouchableOpacity,
    View,
    ViewStyle,
} from 'react-native';

import { BorderRadius, FontSizes, FontWeights, Spacing } from '@/constants/Colors';
import { useTheme } from '@/hooks/useTheme';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  required?: boolean;
  secureTextEntry?: boolean;
  containerStyle?: ViewStyle;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

function Input({
  label,
  error,
  required = false,
  secureTextEntry = false,
  containerStyle,
  leftIcon,
  rightIcon,
  style,
  ...props
}: InputProps) {
  const { colors } = useTheme();
  const [isFocused, setIsFocused] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);

  const styles = StyleSheet.create({
    container: {
      marginBottom: Spacing.md,
    },
    labelContainer: {
      flexDirection: 'row',
      marginBottom: Spacing.sm,
    },
    label: {
      fontSize: FontSizes.md,
      fontWeight: FontWeights.medium,
      color: colors.text,
    },
    required: {
      color: colors.error,
      marginLeft: Spacing.xs,
    },
    inputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.inputBackground,
      borderWidth: 1,
      borderColor: error ? colors.error : isFocused ? colors.inputFocus : colors.inputBorder,
      borderRadius: BorderRadius.md,
      paddingHorizontal: Spacing.md,
    },
    leftIconContainer: {
      marginRight: Spacing.sm,
    },
    input: {
      flex: 1,
      paddingVertical: Spacing.md,
      fontSize: FontSizes.md,
      color: colors.text,
    },
    rightIconContainer: {
      marginLeft: Spacing.sm,
    },
    passwordToggle: {
      padding: Spacing.xs,
    },
    passwordToggleText: {
      fontSize: FontSizes.sm,
      color: colors.primary,
      fontWeight: FontWeights.medium,
    },
    errorText: {
      fontSize: FontSizes.sm,
      color: colors.error,
      marginTop: Spacing.xs,
    },
  });

  const handlePasswordToggle = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <View style={styles.labelContainer}>
          <Text style={styles.label}>{label}</Text>
          {required && <Text style={styles.required}>*</Text>}
        </View>
      )}

      <View style={styles.inputContainer}>
        {leftIcon && (
          <View style={styles.leftIconContainer}>
            {leftIcon}
          </View>
        )}

        <TextInput
          style={[styles.input, style]}
          placeholderTextColor={colors.placeholder}
          secureTextEntry={secureTextEntry && !isPasswordVisible}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          {...props}
        />

        {secureTextEntry && (
          <TouchableOpacity
            style={styles.passwordToggle}
            onPress={handlePasswordToggle}
          >
            <Text style={styles.passwordToggleText}>
              {isPasswordVisible ? 'Hide' : 'Show'}
            </Text>
          </TouchableOpacity>
        )}

        {rightIcon && !secureTextEntry && (
          <View style={styles.rightIconContainer}>
            {rightIcon}
          </View>
        )}
      </View>

      {error && (
        <Text style={styles.errorText}>{error}</Text>
      )}
    </View>
  );
}

export default Input;
