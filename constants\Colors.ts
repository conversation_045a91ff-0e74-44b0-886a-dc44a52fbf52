/**
 * Construction Estimate App Color System
 * Material Design inspired color palette with light and dark theme support
 */

export const Colors = {
  light: {
    // Primary colors
    primary: '#2196F3',
    primaryDark: '#1976D2',
    primaryLight: '#BBDEFB',

    // Secondary colors
    secondary: '#FF9800',
    secondaryDark: '#F57C00',
    secondaryLight: '#FFE0B2',

    // Background colors
    background: '#FFFFFF',
    surface: '#F5F5F5',
    card: '#FFFFFF',

    // Text colors
    text: '#212121',
    textSecondary: '#757575',
    textLight: '#BDBDBD',

    // Status colors
    success: '#4CAF50',
    warning: '#FF9800',
    error: '#F44336',
    info: '#2196F3',

    // Border colors
    border: '#E0E0E0',
    divider: '#EEEEEE',

    // Tab colors
    tint: '#2196F3',
    icon: '#757575',
    tabIconDefault: '#9E9E9E',
    tabIconSelected: '#2196F3',

    // Button colors
    buttonPrimary: '#2196F3',
    buttonSecondary: '#E0E0E0',
    buttonText: '#FFFFFF',
    buttonTextSecondary: '#212121',

    // Input colors
    inputBackground: '#FFFFFF',
    inputBorder: '#E0E0E0',
    inputFocus: '#2196F3',
    placeholder: '#9E9E9E',
  },
  dark: {
    // Primary colors
    primary: '#64B5F6',
    primaryDark: '#42A5F5',
    primaryLight: '#90CAF9',

    // Secondary colors
    secondary: '#FFB74D',
    secondaryDark: '#FFA726',
    secondaryLight: '#FFCC80',

    // Background colors
    background: '#121212',
    surface: '#1E1E1E',
    card: '#2C2C2C',

    // Text colors
    text: '#FFFFFF',
    textSecondary: '#B0B0B0',
    textLight: '#808080',

    // Status colors
    success: '#66BB6A',
    warning: '#FFB74D',
    error: '#EF5350',
    info: '#64B5F6',

    // Border colors
    border: '#404040',
    divider: '#303030',

    // Tab colors
    tint: '#64B5F6',
    icon: '#B0B0B0',
    tabIconDefault: '#808080',
    tabIconSelected: '#64B5F6',

    // Button colors
    buttonPrimary: '#64B5F6',
    buttonSecondary: '#404040',
    buttonText: '#FFFFFF',
    buttonTextSecondary: '#FFFFFF',

    // Input colors
    inputBackground: '#2C2C2C',
    inputBorder: '#404040',
    inputFocus: '#64B5F6',
    placeholder: '#808080',
  },
};

export const Spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const BorderRadius = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  round: 50,
};

export const FontSizes = {
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  xxl: 24,
  xxxl: 32,
};

export const FontWeights = {
  light: '300' as const,
  normal: '400' as const,
  medium: '500' as const,
  semibold: '600' as const,
  bold: '700' as const,
};

export const Shadows = {
  sm: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,
    elevation: 1,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.30,
    shadowRadius: 4.65,
    elevation: 8,
  },
};
