// app/(auth)/login.tsx

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';

import { useTheme } from '@/hooks/useTheme';
import { useAuth } from '@/hooks/useAuth';
import { useForm } from '@/hooks/useForm';
import { USER_VALIDATION_SCHEMA } from '@/utils/validation';
import { Spacing, FontSizes, FontWeights, BorderRadius } from '@/constants/Colors';

interface LoginFormData {
  email: string;
  password: string;
}

export default function LoginScreen() {
  const { colors } = useTheme();
  const { login } = useAuth();
  const [showPassword, setShowPassword] = useState(false);

  const {
    values,
    errors,
    isSubmitting,
    setValue,
    getFieldError,
    hasFieldError,
    handleSubmit,
  } = useForm<LoginFormData>({
    initialValues: {
      email: '',
      password: '',
    },
    validationSchema: {
      email: USER_VALIDATION_SCHEMA.email,
      password: { required: true, minLength: 6 },
    },
    onSubmit: async (formData) => {
      const result = await login(formData.email, formData.password);
      if (result.success) {
        router.replace('/(tabs)/dashboard');
      } else {
        Alert.alert('Login Failed', result.error || 'Please try again');
      }
    },
  });

  const handleBack = () => {
    router.back();
  };

  const handleRegister = () => {
    router.push('/(auth)/register');
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    scrollContainer: {
      flexGrow: 1,
    },
    content: {
      flex: 1,
      padding: Spacing.xl,
      justifyContent: 'center',
    },
    header: {
      alignItems: 'center',
      marginBottom: Spacing.xxl,
    },
    logo: {
      width: 80,
      height: 80,
      borderRadius: BorderRadius.xl,
      backgroundColor: colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: Spacing.lg,
    },
    logoText: {
      fontSize: FontSizes.xxl,
      fontWeight: FontWeights.bold,
      color: '#FFFFFF',
    },
    title: {
      fontSize: FontSizes.xxl,
      fontWeight: FontWeights.bold,
      color: colors.text,
      marginBottom: Spacing.xs,
    },
    subtitle: {
      fontSize: FontSizes.md,
      color: colors.textSecondary,
      textAlign: 'center',
    },
    form: {
      marginBottom: Spacing.xl,
    },
    inputGroup: {
      marginBottom: Spacing.lg,
    },
    label: {
      fontSize: FontSizes.md,
      fontWeight: FontWeights.medium,
      color: colors.text,
      marginBottom: Spacing.sm,
    },
    inputContainer: {
      position: 'relative',
    },
    input: {
      backgroundColor: colors.inputBackground,
      borderWidth: 1,
      borderColor: colors.inputBorder,
      borderRadius: BorderRadius.md,
      paddingHorizontal: Spacing.lg,
      paddingVertical: Spacing.md,
      fontSize: FontSizes.md,
      color: colors.text,
    },
    inputFocused: {
      borderColor: colors.inputFocus,
    },
    inputError: {
      borderColor: colors.error,
    },
    passwordToggle: {
      position: 'absolute',
      right: Spacing.lg,
      top: Spacing.md,
      padding: Spacing.xs,
    },
    passwordToggleText: {
      fontSize: FontSizes.sm,
      color: colors.primary,
      fontWeight: FontWeights.medium,
    },
    errorText: {
      fontSize: FontSizes.sm,
      color: colors.error,
      marginTop: Spacing.xs,
    },
    loginButton: {
      backgroundColor: colors.primary,
      paddingVertical: Spacing.lg,
      borderRadius: BorderRadius.md,
      alignItems: 'center',
      marginBottom: Spacing.lg,
    },
    loginButtonDisabled: {
      backgroundColor: colors.textLight,
    },
    loginButtonText: {
      fontSize: FontSizes.lg,
      fontWeight: FontWeights.semibold,
      color: '#FFFFFF',
    },
    demoSection: {
      backgroundColor: colors.surface,
      padding: Spacing.lg,
      borderRadius: BorderRadius.md,
      marginBottom: Spacing.lg,
    },
    demoTitle: {
      fontSize: FontSizes.md,
      fontWeight: FontWeights.semibold,
      color: colors.text,
      marginBottom: Spacing.sm,
    },
    demoText: {
      fontSize: FontSizes.sm,
      color: colors.textSecondary,
      marginBottom: Spacing.sm,
    },
    demoCredentials: {
      fontSize: FontSizes.sm,
      fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
      color: colors.primary,
    },
    footer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    footerText: {
      fontSize: FontSizes.md,
      color: colors.textSecondary,
    },
    registerLink: {
      fontSize: FontSizes.md,
      color: colors.primary,
      fontWeight: FontWeights.medium,
    },
    backButton: {
      position: 'absolute',
      top: Spacing.lg,
      left: Spacing.lg,
      padding: Spacing.sm,
    },
    backButtonText: {
      fontSize: FontSizes.lg,
      color: colors.primary,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <TouchableOpacity style={styles.backButton} onPress={handleBack}>
        <Text style={styles.backButtonText}>← Back</Text>
      </TouchableOpacity>

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.content}>
            <View style={styles.header}>
              <View style={styles.logo}>
                <Text style={styles.logoText}>CE</Text>
              </View>
              <Text style={styles.title}>Welcome Back</Text>
              <Text style={styles.subtitle}>
                Sign in to your account to continue
              </Text>
            </View>

            <View style={styles.form}>
              <View style={styles.inputGroup}>
                <Text style={styles.label}>Email Address</Text>
                <View style={styles.inputContainer}>
                  <TextInput
                    style={[
                      styles.input,
                      hasFieldError('email') && styles.inputError,
                    ]}
                    value={values.email}
                    onChangeText={(text) => setValue('email', text)}
                    placeholder="Enter your email"
                    placeholderTextColor={colors.placeholder}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                </View>
                {hasFieldError('email') && (
                  <Text style={styles.errorText}>{getFieldError('email')}</Text>
                )}
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.label}>Password</Text>
                <View style={styles.inputContainer}>
                  <TextInput
                    style={[
                      styles.input,
                      hasFieldError('password') && styles.inputError,
                    ]}
                    value={values.password}
                    onChangeText={(text) => setValue('password', text)}
                    placeholder="Enter your password"
                    placeholderTextColor={colors.placeholder}
                    secureTextEntry={!showPassword}
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                  <TouchableOpacity
                    style={styles.passwordToggle}
                    onPress={() => setShowPassword(!showPassword)}
                  >
                    <Text style={styles.passwordToggleText}>
                      {showPassword ? 'Hide' : 'Show'}
                    </Text>
                  </TouchableOpacity>
                </View>
                {hasFieldError('password') && (
                  <Text style={styles.errorText}>{getFieldError('password')}</Text>
                )}
              </View>

              <TouchableOpacity
                style={[
                  styles.loginButton,
                  isSubmitting && styles.loginButtonDisabled,
                ]}
                onPress={handleSubmit}
                disabled={isSubmitting}
              >
                <Text style={styles.loginButtonText}>
                  {isSubmitting ? 'Signing In...' : 'Sign In'}
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.demoSection}>
              <Text style={styles.demoTitle}>Demo Accounts</Text>
              <Text style={styles.demoText}>
                Use these credentials to test different user roles:
              </Text>
              <Text style={styles.demoCredentials}>
                Admin: <EMAIL>{'\n'}
                Manager: <EMAIL>{'\n'}
                Employee: <EMAIL>{'\n'}
                Password: password123
              </Text>
            </View>

            <View style={styles.footer}>
              <Text style={styles.footerText}>Don't have an account? </Text>
              <TouchableOpacity onPress={handleRegister}>
                <Text style={styles.registerLink}>Sign Up</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
