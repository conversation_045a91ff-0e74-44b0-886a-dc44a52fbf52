// app/(auth)/register.tsx

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';

import { useTheme } from '@/hooks/useTheme';
import { useAuth } from '@/hooks/useAuth';
import { useForm } from '@/hooks/useForm';
import { USER_VALIDATION_SCHEMA } from '@/utils/validation';
import { UserRole } from '@/types/models';
import { USER_ROLES } from '@/constants/roles';
import { Spacing, FontSizes, FontWeights, BorderRadius } from '@/constants/Colors';

interface RegisterFormData {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  role: UserRole;
}

export default function RegisterScreen() {
  const { colors } = useTheme();
  const { register } = useAuth();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const {
    values,
    errors,
    isSubmitting,
    setValue,
    getFieldError,
    hasFieldError,
    handleSubmit,
  } = useForm<RegisterFormData>({
    initialValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
      role: 'employee',
    },
    validationSchema: {
      ...USER_VALIDATION_SCHEMA,
      confirmPassword: {
        required: true,
        custom: (value: string) => {
          if (value !== values.password) {
            return 'Passwords do not match';
          }
          return null;
        },
      },
    },
    onSubmit: async (formData) => {
      const result = await register({
        name: formData.name,
        email: formData.email,
        password: formData.password,
        role: formData.role,
      });
      
      if (result.success) {
        Alert.alert(
          'Account Created',
          'Your account has been created successfully!',
          [
            {
              text: 'Continue',
              onPress: () => router.replace('/(tabs)/dashboard'),
            },
          ]
        );
      } else {
        Alert.alert('Registration Failed', result.error || 'Please try again');
      }
    },
  });

  const handleBack = () => {
    router.back();
  };

  const handleLogin = () => {
    router.push('/(auth)/login');
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    scrollContainer: {
      flexGrow: 1,
    },
    content: {
      flex: 1,
      padding: Spacing.xl,
      justifyContent: 'center',
    },
    header: {
      alignItems: 'center',
      marginBottom: Spacing.xl,
    },
    logo: {
      width: 80,
      height: 80,
      borderRadius: BorderRadius.xl,
      backgroundColor: colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: Spacing.lg,
    },
    logoText: {
      fontSize: FontSizes.xxl,
      fontWeight: FontWeights.bold,
      color: '#FFFFFF',
    },
    title: {
      fontSize: FontSizes.xxl,
      fontWeight: FontWeights.bold,
      color: colors.text,
      marginBottom: Spacing.xs,
    },
    subtitle: {
      fontSize: FontSizes.md,
      color: colors.textSecondary,
      textAlign: 'center',
    },
    form: {
      marginBottom: Spacing.lg,
    },
    inputGroup: {
      marginBottom: Spacing.lg,
    },
    label: {
      fontSize: FontSizes.md,
      fontWeight: FontWeights.medium,
      color: colors.text,
      marginBottom: Spacing.sm,
    },
    inputContainer: {
      position: 'relative',
    },
    input: {
      backgroundColor: colors.inputBackground,
      borderWidth: 1,
      borderColor: colors.inputBorder,
      borderRadius: BorderRadius.md,
      paddingHorizontal: Spacing.lg,
      paddingVertical: Spacing.md,
      fontSize: FontSizes.md,
      color: colors.text,
    },
    inputError: {
      borderColor: colors.error,
    },
    passwordToggle: {
      position: 'absolute',
      right: Spacing.lg,
      top: Spacing.md,
      padding: Spacing.xs,
    },
    passwordToggleText: {
      fontSize: FontSizes.sm,
      color: colors.primary,
      fontWeight: FontWeights.medium,
    },
    errorText: {
      fontSize: FontSizes.sm,
      color: colors.error,
      marginTop: Spacing.xs,
    },
    roleSelector: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: Spacing.sm,
    },
    roleOption: {
      flex: 1,
      minWidth: '45%',
      backgroundColor: colors.surface,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: BorderRadius.md,
      padding: Spacing.md,
      alignItems: 'center',
    },
    roleOptionSelected: {
      backgroundColor: colors.primary,
      borderColor: colors.primary,
    },
    roleOptionText: {
      fontSize: FontSizes.sm,
      fontWeight: FontWeights.medium,
      color: colors.text,
    },
    roleOptionTextSelected: {
      color: '#FFFFFF',
    },
    registerButton: {
      backgroundColor: colors.primary,
      paddingVertical: Spacing.lg,
      borderRadius: BorderRadius.md,
      alignItems: 'center',
      marginBottom: Spacing.lg,
    },
    registerButtonDisabled: {
      backgroundColor: colors.textLight,
    },
    registerButtonText: {
      fontSize: FontSizes.lg,
      fontWeight: FontWeights.semibold,
      color: '#FFFFFF',
    },
    footer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    footerText: {
      fontSize: FontSizes.md,
      color: colors.textSecondary,
    },
    loginLink: {
      fontSize: FontSizes.md,
      color: colors.primary,
      fontWeight: FontWeights.medium,
    },
    backButton: {
      position: 'absolute',
      top: Spacing.lg,
      left: Spacing.lg,
      padding: Spacing.sm,
    },
    backButtonText: {
      fontSize: FontSizes.lg,
      color: colors.primary,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <TouchableOpacity style={styles.backButton} onPress={handleBack}>
        <Text style={styles.backButtonText}>← Back</Text>
      </TouchableOpacity>

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.content}>
            <View style={styles.header}>
              <View style={styles.logo}>
                <Text style={styles.logoText}>CE</Text>
              </View>
              <Text style={styles.title}>Create Account</Text>
              <Text style={styles.subtitle}>
                Join thousands of construction professionals
              </Text>
            </View>

            <View style={styles.form}>
              <View style={styles.inputGroup}>
                <Text style={styles.label}>Full Name</Text>
                <TextInput
                  style={[
                    styles.input,
                    hasFieldError('name') && styles.inputError,
                  ]}
                  value={values.name}
                  onChangeText={(text) => setValue('name', text)}
                  placeholder="Enter your full name"
                  placeholderTextColor={colors.placeholder}
                  autoCapitalize="words"
                />
                {hasFieldError('name') && (
                  <Text style={styles.errorText}>{getFieldError('name')}</Text>
                )}
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.label}>Email Address</Text>
                <TextInput
                  style={[
                    styles.input,
                    hasFieldError('email') && styles.inputError,
                  ]}
                  value={values.email}
                  onChangeText={(text) => setValue('email', text)}
                  placeholder="Enter your email"
                  placeholderTextColor={colors.placeholder}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                {hasFieldError('email') && (
                  <Text style={styles.errorText}>{getFieldError('email')}</Text>
                )}
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.label}>Password</Text>
                <View style={styles.inputContainer}>
                  <TextInput
                    style={[
                      styles.input,
                      hasFieldError('password') && styles.inputError,
                    ]}
                    value={values.password}
                    onChangeText={(text) => setValue('password', text)}
                    placeholder="Create a password"
                    placeholderTextColor={colors.placeholder}
                    secureTextEntry={!showPassword}
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                  <TouchableOpacity
                    style={styles.passwordToggle}
                    onPress={() => setShowPassword(!showPassword)}
                  >
                    <Text style={styles.passwordToggleText}>
                      {showPassword ? 'Hide' : 'Show'}
                    </Text>
                  </TouchableOpacity>
                </View>
                {hasFieldError('password') && (
                  <Text style={styles.errorText}>{getFieldError('password')}</Text>
                )}
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.label}>Confirm Password</Text>
                <View style={styles.inputContainer}>
                  <TextInput
                    style={[
                      styles.input,
                      hasFieldError('confirmPassword') && styles.inputError,
                    ]}
                    value={values.confirmPassword}
                    onChangeText={(text) => setValue('confirmPassword', text)}
                    placeholder="Confirm your password"
                    placeholderTextColor={colors.placeholder}
                    secureTextEntry={!showConfirmPassword}
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                  <TouchableOpacity
                    style={styles.passwordToggle}
                    onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    <Text style={styles.passwordToggleText}>
                      {showConfirmPassword ? 'Hide' : 'Show'}
                    </Text>
                  </TouchableOpacity>
                </View>
                {hasFieldError('confirmPassword') && (
                  <Text style={styles.errorText}>{getFieldError('confirmPassword')}</Text>
                )}
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.label}>Role</Text>
                <View style={styles.roleSelector}>
                  {(Object.keys(USER_ROLES) as UserRole[])
                    .filter(role => role !== 'admin') // Hide admin role from registration
                    .map((role) => (
                    <TouchableOpacity
                      key={role}
                      style={[
                        styles.roleOption,
                        values.role === role && styles.roleOptionSelected,
                      ]}
                      onPress={() => setValue('role', role)}
                    >
                      <Text
                        style={[
                          styles.roleOptionText,
                          values.role === role && styles.roleOptionTextSelected,
                        ]}
                      >
                        {USER_ROLES[role].label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              <TouchableOpacity
                style={[
                  styles.registerButton,
                  isSubmitting && styles.registerButtonDisabled,
                ]}
                onPress={handleSubmit}
                disabled={isSubmitting}
              >
                <Text style={styles.registerButtonText}>
                  {isSubmitting ? 'Creating Account...' : 'Create Account'}
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.footer}>
              <Text style={styles.footerText}>Already have an account? </Text>
              <TouchableOpacity onPress={handleLogin}>
                <Text style={styles.loginLink}>Sign In</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
