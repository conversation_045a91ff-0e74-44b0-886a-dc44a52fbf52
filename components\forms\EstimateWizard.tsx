// components/forms/EstimateWizard.tsx

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';

import { useTheme } from '@/hooks/useTheme';
import { useAuth } from '@/hooks/useAuth';
import { Estimate, EstimateItem, Client } from '@/types/models';
import { estimateApi, clientApi } from '@/utils/api';
import { calculateEstimateTotal, generateEstimateNumber } from '@/utils/calculations';
import { Spacing, FontSizes, FontWeights } from '@/constants/Colors';

import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import EstimateStep1 from './EstimateStep1';
import EstimateStep2 from './EstimateStep2';
import EstimateStep3 from './EstimateStep3';

interface EstimateWizardProps {
  estimate?: Estimate;
  onSuccess: (estimate: Estimate) => void;
  onCancel: () => void;
}

export interface EstimateFormData {
  clientId: string;
  projectName: string;
  projectDescription: string;
  startDate: string;
  endDate: string;
  items: EstimateItem[];
  notes: string;
  taxRate: number;
  discountPercent: number;
}

export default function EstimateWizard({ estimate, onSuccess, onCancel }: EstimateWizardProps) {
  const { colors } = useTheme();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [clients, setClients] = useState<Client[]>([]);

  const [formData, setFormData] = useState<EstimateFormData>({
    clientId: estimate?.clientId || '',
    projectName: estimate?.projectName || '',
    projectDescription: estimate?.projectDescription || '',
    startDate: estimate?.startDate || new Date().toISOString().split('T')[0],
    endDate: estimate?.endDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    items: estimate?.items || [],
    notes: estimate?.notes || '',
    taxRate: 0.08, // 8% default tax rate
    discountPercent: 0,
  });

  React.useEffect(() => {
    loadClients();
  }, []);

  const loadClients = async () => {
    try {
      const response = await clientApi.getAll();
      if (response.success && response.data) {
        setClients(response.data);
      }
    } catch (error) {
      console.error('Error loading clients:', error);
    }
  };

  const updateFormData = (updates: Partial<EstimateFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  const handleNext = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSaveDraft = async () => {
    await handleSubmit('draft');
  };

  const handleFinalize = async () => {
    await handleSubmit('finalized');
  };

  const handleSubmit = async (status: 'draft' | 'finalized') => {
    if (!formData.clientId || !formData.projectName || formData.items.length === 0) {
      Alert.alert('Validation Error', 'Please fill in all required fields and add at least one item.');
      return;
    }

    setIsSubmitting(true);
    try {
      const calculations = calculateEstimateTotal(formData.items, formData.taxRate, formData.discountPercent);
      
      const estimateData: Omit<Estimate, 'id' | 'createdAt' | 'updatedAt'> = {
        clientId: formData.clientId,
        projectName: formData.projectName,
        projectDescription: formData.projectDescription,
        startDate: formData.startDate,
        endDate: formData.endDate,
        items: formData.items,
        notes: formData.notes,
        status,
        createdBy: user?.id || '',
        ...calculations,
      };

      let result;
      if (estimate) {
        result = await estimateApi.update(estimate.id, estimateData);
      } else {
        result = await estimateApi.create(estimateData);
      }

      if (result.success && result.data) {
        Alert.alert(
          'Success',
          `Estimate ${status === 'draft' ? 'saved as draft' : 'finalized'} successfully!`,
          [
            {
              text: 'OK',
              onPress: () => onSuccess(result.data!),
            },
          ]
        );
      } else {
        Alert.alert('Error', result.error || 'Failed to save estimate');
      }
    } catch (error) {
      console.error('Estimate submission error:', error);
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case 1:
        return 'Project Details';
      case 2:
        return 'Add Items';
      case 3:
        return 'Review & Finalize';
      default:
        return 'Estimate';
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    header: {
      marginBottom: Spacing.lg,
    },
    title: {
      fontSize: FontSizes.xl,
      fontWeight: FontWeights.bold,
      color: colors.text,
      marginBottom: Spacing.xs,
    },
    subtitle: {
      fontSize: FontSizes.md,
      color: colors.textSecondary,
    },
    stepIndicator: {
      flexDirection: 'row',
      justifyContent: 'center',
      marginBottom: Spacing.xl,
    },
    step: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.surface,
      borderWidth: 2,
      borderColor: colors.border,
      justifyContent: 'center',
      alignItems: 'center',
      marginHorizontal: Spacing.sm,
    },
    stepActive: {
      backgroundColor: colors.primary,
      borderColor: colors.primary,
    },
    stepCompleted: {
      backgroundColor: colors.success,
      borderColor: colors.success,
    },
    stepNumber: {
      fontSize: FontSizes.md,
      fontWeight: FontWeights.semibold,
      color: colors.textSecondary,
    },
    stepNumberActive: {
      color: '#FFFFFF',
    },
    stepConnector: {
      flex: 1,
      height: 2,
      backgroundColor: colors.border,
      alignSelf: 'center',
    },
    stepConnectorCompleted: {
      backgroundColor: colors.success,
    },
    content: {
      flex: 1,
      marginBottom: Spacing.lg,
    },
    buttonContainer: {
      flexDirection: 'row',
      gap: Spacing.md,
    },
    button: {
      flex: 1,
    },
  });

  const renderStepIndicator = () => (
    <View style={styles.stepIndicator}>
      {[1, 2, 3].map((step, index) => (
        <React.Fragment key={step}>
          <View
            style={[
              styles.step,
              currentStep === step && styles.stepActive,
              currentStep > step && styles.stepCompleted,
            ]}
          >
            <Text
              style={[
                styles.stepNumber,
                (currentStep === step || currentStep > step) && styles.stepNumberActive,
              ]}
            >
              {step}
            </Text>
          </View>
          {index < 2 && (
            <View
              style={[
                styles.stepConnector,
                currentStep > step && styles.stepConnectorCompleted,
              ]}
            />
          )}
        </React.Fragment>
      ))}
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <EstimateStep1
            formData={formData}
            updateFormData={updateFormData}
            clients={clients}
          />
        );
      case 2:
        return (
          <EstimateStep2
            formData={formData}
            updateFormData={updateFormData}
          />
        );
      case 3:
        return (
          <EstimateStep3
            formData={formData}
            updateFormData={updateFormData}
            clients={clients}
          />
        );
      default:
        return null;
    }
  };

  const renderButtons = () => {
    if (currentStep === 1) {
      return (
        <View style={styles.buttonContainer}>
          <Button
            title="Cancel"
            onPress={onCancel}
            variant="outline"
            style={styles.button}
          />
          <Button
            title="Next"
            onPress={handleNext}
            style={styles.button}
          />
        </View>
      );
    }

    if (currentStep === 2) {
      return (
        <View style={styles.buttonContainer}>
          <Button
            title="Previous"
            onPress={handlePrevious}
            variant="outline"
            style={styles.button}
          />
          <Button
            title="Next"
            onPress={handleNext}
            style={styles.button}
          />
        </View>
      );
    }

    if (currentStep === 3) {
      return (
        <View style={styles.buttonContainer}>
          <Button
            title="Previous"
            onPress={handlePrevious}
            variant="outline"
            style={styles.button}
          />
          <Button
            title="Save Draft"
            onPress={handleSaveDraft}
            variant="secondary"
            loading={isSubmitting}
            style={styles.button}
          />
          <Button
            title="Finalize"
            onPress={handleFinalize}
            loading={isSubmitting}
            style={styles.button}
          />
        </View>
      );
    }

    return null;
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>
          {estimate ? 'Edit Estimate' : 'Create New Estimate'}
        </Text>
        <Text style={styles.subtitle}>
          Step {currentStep} of 3: {getStepTitle()}
        </Text>
      </View>

      {renderStepIndicator()}

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Card>
          {renderCurrentStep()}
        </Card>
      </ScrollView>

      {renderButtons()}
    </View>
  );
}
