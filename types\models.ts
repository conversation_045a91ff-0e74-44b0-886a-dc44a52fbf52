// types/models.ts

export type UserRole = 'admin' | 'manager' | 'employee' | 'client';

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  createdAt: string;
  updatedAt: string;
}

export interface Client {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  status: 'active' | 'inactive' | 'prospect';
  projects: string[]; // estimate IDs
  createdAt: string;
  updatedAt: string;
}

export interface EstimateItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
  category?: string;
  notes?: string;
}

export interface Estimate {
  id: string;
  clientId: string;
  projectName: string;
  projectDescription: string;
  startDate: string;
  endDate: string;
  items: EstimateItem[];
  subtotal: number;
  taxes: number;
  discount: number;
  grandTotal: number;
  status: 'draft' | 'finalized' | 'sent' | 'approved' | 'rejected';
  notes?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string; // user ID
}

export interface Template {
  id: string;
  name: string;
  category: string;
  description?: string;
  defaultItems: EstimateItem[];
  createdAt: string;
  updatedAt: string;
  createdBy: string; // user ID
}

export interface DashboardStats {
  totalEstimates: number;
  pendingEstimates: number;
  totalRevenue: number;
  activeProjects: number;
  recentActivity: ActivityItem[];
}

export interface ActivityItem {
  id: string;
  type: 'estimate_created' | 'estimate_updated' | 'client_added' | 'template_created';
  description: string;
  timestamp: string;
  userId: string;
}

export interface AppSettings {
  theme: 'light' | 'dark' | 'system';
  notifications: boolean;
  companyInfo: CompanyInfo;
  taxRate: number;
  currency: string;
}

export interface CompanyInfo {
  name: string;
  address: string;
  phone: string;
  email: string;
  website?: string;
  logo?: string;
}

// Form validation types
export interface ValidationError {
  field: string;
  message: string;
}

export interface FormState<T> {
  data: T;
  errors: ValidationError[];
  isValid: boolean;
  isSubmitting: boolean;
}

// API response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Navigation types
export type RootStackParamList = {
  '(auth)': undefined;
  '(tabs)': undefined;
};

export type AuthStackParamList = {
  welcome: undefined;
  login: undefined;
  register: undefined;
};

export type TabsParamList = {
  dashboard: undefined;
  clients: undefined;
  estimates: undefined;
  templates: undefined;
  settings: undefined;
};
