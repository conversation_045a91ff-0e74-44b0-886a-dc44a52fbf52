
# 📘 User Guidelines for Augment Chat
### Construction Estimate Management App (React Native + Expo Router)
**Project Root Path:** `/Users/<USER>/Documents/EstimateApp`

---

## 🚀 Purpose

This document provides **clear and actionable guidelines** for Augment Chat to assist in developing and maintaining the Construction Estimate Management App. The goal is to ensure **consistent, high-quality, and professional results** throughout the project lifecycle.

---

## 📂 Project Setup Reference

Always refer to the **latest official Expo documentation** for setup and configuration:  
🔗 [https://docs.expo.dev/get-started/create-a-project/](https://docs.expo.dev/get-started/create-a-project/)

---

## 📋 Guidelines for Augment Chat

### 1️⃣ Project Structure & Coding Standards

✅ Follow the established **file structure**:

```
/Users/<USER>/Documents/EstimateApp/
├── app/
│   ├── (tabs)/
│   ├── (auth)/
├── components/
├── context/
├── hooks/
├── types/
├── utils/
├── constants/
```

✅ Use **TypeScript** consistently across the entire codebase  
✅ Follow **React Native + Expo best practices**  
✅ Use **`StyleSheet.create` for all styling** (do not use Tailwind, NativeWind, or CSS-in-JS libraries)  
✅ Keep code **modular**: break down into **components**, **hooks**, **context**, **utils**  
✅ Use **TypeScript types and interfaces** for all data models and props  
✅ Write **clean, readable, and well-commented code**

---

### 2️⃣ Feature Implementation

✅ Implement **tab-based navigation** as the primary pattern  
✅ Use **Expo Router** for routing and navigation  
✅ Use **React Context API** for global state management:
- Authentication
- Theme (light/dark)
- App settings

✅ Build **reusable components** for buttons, forms, cards, modals  
✅ Implement **form validation** for all inputs  
✅ Create **mock data and utilities** for prototyping features  
✅ Add **loading indicators** and **error handling** for all async operations  
✅ Ensure **responsive design** for phones and tablets

---

### 3️⃣ Technical Consistency

✅ Follow **Material Design** principles for UI/UX  
✅ Implement **dark/light theme support** with system preference detection  
✅ Prioritize **accessibility**: screen reader support, high-contrast colors, proper semantics  
✅ Use **FlatList** for rendering large lists  
✅ Include **error boundaries** to catch unexpected errors  
✅ Follow **React Native best practices** for performance optimization

---

### 4️⃣ Documentation & References

✅ Use the latest official Expo documentation:  
🔗 [https://docs.expo.dev/get-started/create-a-project/](https://docs.expo.dev/get-started/create-a-project/)

✅ Reference this **User Guidelines** as the standard for Augment Chat’s contributions

---

### 5️⃣ Project-Specific Notes

✅ The project root path is:
```
/Users/<USER>/Documents/EstimateApp
```

✅ All files and folders should be created and maintained under this root path

✅ Any new feature, update, or modification should align with the **overall architecture and best practices** outlined in this document

✅ If any part of the guidelines is unclear or a technical decision is uncertain, **ask Ermir** for clarification before proceeding

---
