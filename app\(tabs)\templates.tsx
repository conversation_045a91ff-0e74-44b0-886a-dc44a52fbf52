// app/(tabs)/templates.tsx

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { useTheme } from '@/hooks/useTheme';
import { useAuth } from '@/hooks/useAuth';
import { Template } from '@/types/models';
import { templateApi } from '@/utils/api';
import { TRADE_CATEGORIES } from '@/constants/categories';
import { Spacing, FontSizes, FontWeights } from '@/constants/Colors';

export default function TemplatesScreen() {
  const { colors } = useTheme();
  const { hasPermission } = useAuth();
  const [templates, setTemplates] = useState<Template[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      const response = await templateApi.getAll();
      if (response.success && response.data) {
        setTemplates(response.data);
      } else {
        Alert.alert('Error', response.error || 'Failed to load templates');
      }
    } catch (error) {
      console.error('Templates error:', error);
      Alert.alert('Error', 'Failed to load templates');
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadTemplates();
    setRefreshing(false);
  };

  const getCategoryConfig = (categoryId: string) => {
    return TRADE_CATEGORIES.find(c => c.id === categoryId) || TRADE_CATEGORIES[0];
  };

  const renderTemplateItem = ({ item }: { item: Template }) => {
    const categoryConfig = getCategoryConfig(item.category);
    
    return (
      <TouchableOpacity style={styles.templateCard}>
        <View style={styles.templateHeader}>
          <View style={styles.templateInfo}>
            <Text style={styles.templateName}>{item.name}</Text>
            <View style={[styles.categoryBadge, { backgroundColor: categoryConfig.color }]}>
              <Text style={styles.categoryText}>{categoryConfig.name}</Text>
            </View>
          </View>
        </View>
        
        {item.description && (
          <Text style={styles.templateDescription} numberOfLines={2}>
            {item.description}
          </Text>
        )}
        
        <View style={styles.templateDetails}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Items:</Text>
            <Text style={styles.detailValue}>{item.defaultItems.length}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Category:</Text>
            <Text style={styles.detailValue}>{categoryConfig.name}</Text>
          </View>
        </View>

        <View style={styles.templateFooter}>
          <Text style={styles.templateDate}>
            Created {new Date(item.createdAt).toLocaleDateString()}
          </Text>
          <TouchableOpacity style={styles.useButton}>
            <Text style={styles.useButtonText}>Use Template</Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      padding: Spacing.lg,
      backgroundColor: colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    title: {
      fontSize: FontSizes.xxl,
      fontWeight: FontWeights.bold,
      color: colors.text,
      marginBottom: Spacing.xs,
    },
    subtitle: {
      fontSize: FontSizes.md,
      color: colors.textSecondary,
    },
    content: {
      flex: 1,
      padding: Spacing.lg,
    },
    templateCard: {
      backgroundColor: colors.card,
      padding: Spacing.lg,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: colors.border,
      marginBottom: Spacing.md,
    },
    templateHeader: {
      marginBottom: Spacing.sm,
    },
    templateInfo: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    templateName: {
      fontSize: FontSizes.lg,
      fontWeight: FontWeights.semibold,
      color: colors.text,
      flex: 1,
    },
    categoryBadge: {
      paddingHorizontal: Spacing.sm,
      paddingVertical: Spacing.xs,
      borderRadius: 12,
    },
    categoryText: {
      fontSize: FontSizes.xs,
      fontWeight: FontWeights.medium,
      color: '#FFFFFF',
    },
    templateDescription: {
      fontSize: FontSizes.md,
      color: colors.textSecondary,
      marginBottom: Spacing.md,
    },
    templateDetails: {
      marginBottom: Spacing.md,
    },
    detailRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: Spacing.xs,
    },
    detailLabel: {
      fontSize: FontSizes.sm,
      color: colors.textSecondary,
    },
    detailValue: {
      fontSize: FontSizes.sm,
      fontWeight: FontWeights.medium,
      color: colors.text,
    },
    templateFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    templateDate: {
      fontSize: FontSizes.xs,
      color: colors.textLight,
    },
    useButton: {
      backgroundColor: colors.primary,
      paddingHorizontal: Spacing.md,
      paddingVertical: Spacing.sm,
      borderRadius: 8,
    },
    useButtonText: {
      fontSize: FontSizes.sm,
      fontWeight: FontWeights.medium,
      color: '#FFFFFF',
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: Spacing.xl,
    },
    emptyText: {
      fontSize: FontSizes.lg,
      color: colors.textSecondary,
      textAlign: 'center',
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      fontSize: FontSizes.md,
      color: colors.textSecondary,
    },
  });

  if (isLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading templates...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Templates</Text>
        <Text style={styles.subtitle}>
          Reusable estimate templates for common projects
        </Text>
      </View>

      <View style={styles.content}>
        <FlatList
          data={templates}
          renderItem={renderTemplateItem}
          keyExtractor={(item) => item.id}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>
                No templates found.{'\n'}
                {hasPermission('manage_templates') 
                  ? 'Create your first template to speed up estimates!' 
                  : 'Contact your administrator to create templates.'}
              </Text>
            </View>
          }
          showsVerticalScrollIndicator={false}
        />
      </View>
    </SafeAreaView>
  );
}
