// constants/roles.ts

import { UserRole } from '@/types/models';

export const USER_ROLES: Record<UserRole, { label: string; permissions: string[] }> = {
  admin: {
    label: 'Administrator',
    permissions: [
      'manage_users',
      'manage_clients',
      'manage_estimates',
      'manage_templates',
      'view_analytics',
      'export_data',
      'manage_settings',
    ],
  },
  manager: {
    label: 'Manager',
    permissions: [
      'manage_clients',
      'manage_estimates',
      'manage_templates',
      'view_analytics',
      'export_data',
    ],
  },
  employee: {
    label: 'Employee',
    permissions: [
      'view_clients',
      'manage_estimates',
      'view_templates',
      'use_templates',
    ],
  },
  client: {
    label: 'Client',
    permissions: [
      'view_own_estimates',
      'approve_estimates',
      'reject_estimates',
    ],
  },
};

export const ROLE_HIERARCHY: Record<UserRole, number> = {
  admin: 4,
  manager: 3,
  employee: 2,
  client: 1,
};

export const hasPermission = (userRole: UserRole, permission: string): boolean => {
  return USER_ROLES[userRole].permissions.includes(permission);
};

export const canAccessRole = (currentRole: UserRole, targetRole: UserRole): boolean => {
  return ROLE_HIERARCHY[currentRole] >= ROLE_HIERARCHY[targetRole];
};
