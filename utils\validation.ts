// utils/validation.ts

import { ValidationError } from '@/types/models';

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
}

export interface ValidationSchema {
  [key: string]: ValidationRule;
}

export const validateField = (value: any, rules: ValidationRule): string | null => {
  // Required validation
  if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
    return 'This field is required';
  }

  // Skip other validations if field is empty and not required
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    return null;
  }

  // String validations
  if (typeof value === 'string') {
    if (rules.minLength && value.length < rules.minLength) {
      return `Must be at least ${rules.minLength} characters`;
    }

    if (rules.maxLength && value.length > rules.maxLength) {
      return `Must be no more than ${rules.maxLength} characters`;
    }

    if (rules.pattern && !rules.pattern.test(value)) {
      return 'Invalid format';
    }
  }

  // Custom validation
  if (rules.custom) {
    return rules.custom(value);
  }

  return null;
};

export const validateForm = (data: Record<string, any>, schema: ValidationSchema): ValidationError[] => {
  const errors: ValidationError[] = [];

  Object.keys(schema).forEach(field => {
    const value = data[field];
    const rules = schema[field];
    const error = validateField(value, rules);

    if (error) {
      errors.push({ field, message: error });
    }
  });

  return errors;
};

// Common validation patterns
export const VALIDATION_PATTERNS = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^[\+]?[1-9][\d]{0,15}$/,
  url: /^https?:\/\/.+/,
  number: /^\d+(\.\d+)?$/,
  currency: /^\d+(\.\d{1,2})?$/,
};

// Common validation schemas
export const USER_VALIDATION_SCHEMA: ValidationSchema = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 50,
  },
  email: {
    required: true,
    pattern: VALIDATION_PATTERNS.email,
  },
  password: {
    required: true,
    minLength: 6,
    custom: (value: string) => {
      if (!/(?=.*[a-z])/.test(value)) {
        return 'Password must contain at least one lowercase letter';
      }
      if (!/(?=.*[A-Z])/.test(value)) {
        return 'Password must contain at least one uppercase letter';
      }
      if (!/(?=.*\d)/.test(value)) {
        return 'Password must contain at least one number';
      }
      return null;
    },
  },
};

export const CLIENT_VALIDATION_SCHEMA: ValidationSchema = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 100,
  },
  email: {
    required: true,
    pattern: VALIDATION_PATTERNS.email,
  },
  phone: {
    required: true,
    pattern: VALIDATION_PATTERNS.phone,
  },
  address: {
    required: true,
    minLength: 10,
    maxLength: 200,
  },
};

export const ESTIMATE_VALIDATION_SCHEMA: ValidationSchema = {
  projectName: {
    required: true,
    minLength: 3,
    maxLength: 100,
  },
  projectDescription: {
    required: true,
    minLength: 10,
    maxLength: 500,
  },
  startDate: {
    required: true,
    custom: (value: string) => {
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        return 'Invalid date';
      }
      if (date < new Date()) {
        return 'Start date cannot be in the past';
      }
      return null;
    },
  },
  endDate: {
    required: true,
    custom: (value: string) => {
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        return 'Invalid date';
      }
      return null;
    },
  },
};

export const ESTIMATE_ITEM_VALIDATION_SCHEMA: ValidationSchema = {
  description: {
    required: true,
    minLength: 3,
    maxLength: 200,
  },
  quantity: {
    required: true,
    custom: (value: number) => {
      if (value <= 0) {
        return 'Quantity must be greater than 0';
      }
      return null;
    },
  },
  unitPrice: {
    required: true,
    custom: (value: number) => {
      if (value < 0) {
        return 'Unit price cannot be negative';
      }
      return null;
    },
  },
};

export const TEMPLATE_VALIDATION_SCHEMA: ValidationSchema = {
  name: {
    required: true,
    minLength: 3,
    maxLength: 50,
  },
  category: {
    required: true,
  },
  description: {
    maxLength: 200,
  },
};
