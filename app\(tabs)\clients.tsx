// app/(tabs)/clients.tsx

import React, { useEffect, useState } from 'react';
import {
    <PERSON><PERSON>,
    FlatList,
    RefreshControl,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import ClientForm from '../../components/forms/ClientForm';
import Button from '../../components/ui/Button';
import Modal from '../../components/ui/Modal';

import { CLIENT_STATUSES } from '@/constants/categories';
import { FontSizes, FontWeights, Spacing } from '@/constants/Colors';
import { useAuth } from '@/hooks/useAuth';
import { useTheme } from '@/hooks/useTheme';
import { Client } from '@/types/models';
import { clientApi } from '@/utils/api';

export default function ClientsScreen() {
  const { colors } = useTheme();
  const { hasPermission } = useAuth();
  const [clients, setClients] = useState<Client[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [selectedClient, setSelectedClient] = useState<Client | undefined>();

  useEffect(() => {
    loadClients();
  }, []);

  const loadClients = async () => {
    try {
      const response = await clientApi.getAll();
      if (response.success && response.data) {
        setClients(response.data);
      } else {
        Alert.alert('Error', response.error || 'Failed to load clients');
      }
    } catch (error) {
      console.error('Clients error:', error);
      Alert.alert('Error', 'Failed to load clients');
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadClients();
    setRefreshing(false);
  };

  const handleAddClient = () => {
    setSelectedClient(undefined);
    setShowModal(true);
  };

  const handleEditClient = (client: Client) => {
    setSelectedClient(client);
    setShowModal(true);
  };

  const handleDeleteClient = (client: Client) => {
    Alert.alert(
      'Delete Client',
      `Are you sure you want to delete ${client.name}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const result = await clientApi.delete(client.id);
            if (result.success) {
              setClients(prev => prev.filter(c => c.id !== client.id));
              Alert.alert('Success', 'Client deleted successfully');
            } else {
              Alert.alert('Error', result.error || 'Failed to delete client');
            }
          },
        },
      ]
    );
  };

  const handleFormSuccess = (client: Client) => {
    setShowModal(false);
    setSelectedClient(undefined);
    loadClients(); // Refresh the list
  };

  const handleFormCancel = () => {
    setShowModal(false);
    setSelectedClient(undefined);
  };

  const getStatusConfig = (status: string) => {
    return CLIENT_STATUSES.find(s => s.value === status) || CLIENT_STATUSES[0];
  };

  const renderClientItem = ({ item }: { item: Client }) => {
    const statusConfig = getStatusConfig(item.status);

    return (
      <View style={styles.clientCard}>
        <View style={styles.clientHeader}>
          <Text style={styles.clientName}>{item.name}</Text>
          <View style={[styles.statusBadge, { backgroundColor: statusConfig.color }]}>
            <Text style={styles.statusText}>{statusConfig.label}</Text>
          </View>
        </View>
        <Text style={styles.clientEmail}>{item.email}</Text>
        <Text style={styles.clientPhone}>{item.phone}</Text>
        <Text style={styles.clientAddress} numberOfLines={2}>
          {item.address}
        </Text>
        <View style={styles.clientFooter}>
          <View style={styles.clientInfo}>
            <Text style={styles.projectCount}>
              {item.projects.length} project{item.projects.length !== 1 ? 's' : ''}
            </Text>
            <Text style={styles.clientDate}>
              Added {new Date(item.createdAt).toLocaleDateString()}
            </Text>
          </View>
          {hasPermission('manage_clients') && (
            <View style={styles.clientActions}>
              <TouchableOpacity
                style={styles.editButton}
                onPress={() => handleEditClient(item)}
              >
                <Text style={styles.editButtonText}>Edit</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.deleteButton}
                onPress={() => handleDeleteClient(item)}
              >
                <Text style={styles.deleteButtonText}>Delete</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      padding: Spacing.lg,
      backgroundColor: colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerContent: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
    },
    headerText: {
      flex: 1,
      marginRight: Spacing.md,
    },
    title: {
      fontSize: FontSizes.xxl,
      fontWeight: FontWeights.bold,
      color: colors.text,
      marginBottom: Spacing.xs,
    },
    subtitle: {
      fontSize: FontSizes.md,
      color: colors.textSecondary,
    },
    content: {
      flex: 1,
      padding: Spacing.lg,
    },
    clientCard: {
      backgroundColor: colors.card,
      padding: Spacing.lg,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: colors.border,
      marginBottom: Spacing.md,
    },
    clientHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: Spacing.sm,
    },
    clientName: {
      fontSize: FontSizes.lg,
      fontWeight: FontWeights.semibold,
      color: colors.text,
      flex: 1,
    },
    statusBadge: {
      paddingHorizontal: Spacing.sm,
      paddingVertical: Spacing.xs,
      borderRadius: 12,
    },
    statusText: {
      fontSize: FontSizes.xs,
      fontWeight: FontWeights.medium,
      color: '#FFFFFF',
    },
    clientEmail: {
      fontSize: FontSizes.md,
      color: colors.primary,
      marginBottom: Spacing.xs,
    },
    clientPhone: {
      fontSize: FontSizes.md,
      color: colors.textSecondary,
      marginBottom: Spacing.xs,
    },
    clientAddress: {
      fontSize: FontSizes.sm,
      color: colors.textSecondary,
      marginBottom: Spacing.sm,
    },
    clientFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-end',
      marginTop: Spacing.sm,
    },
    clientInfo: {
      flex: 1,
    },
    clientActions: {
      flexDirection: 'row',
      gap: Spacing.sm,
    },
    editButton: {
      backgroundColor: colors.primary,
      paddingHorizontal: Spacing.md,
      paddingVertical: Spacing.sm,
      borderRadius: 6,
    },
    editButtonText: {
      fontSize: FontSizes.sm,
      fontWeight: FontWeights.medium,
      color: '#FFFFFF',
    },
    deleteButton: {
      backgroundColor: colors.error,
      paddingHorizontal: Spacing.md,
      paddingVertical: Spacing.sm,
      borderRadius: 6,
    },
    deleteButtonText: {
      fontSize: FontSizes.sm,
      fontWeight: FontWeights.medium,
      color: '#FFFFFF',
    },
    projectCount: {
      fontSize: FontSizes.sm,
      fontWeight: FontWeights.medium,
      color: colors.primary,
    },
    clientDate: {
      fontSize: FontSizes.xs,
      color: colors.textLight,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: Spacing.xl,
    },
    emptyText: {
      fontSize: FontSizes.lg,
      color: colors.textSecondary,
      textAlign: 'center',
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      fontSize: FontSizes.md,
      color: colors.textSecondary,
    },
  });

  if (isLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading clients...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.headerText}>
            <Text style={styles.title}>Clients</Text>
            <Text style={styles.subtitle}>
              Manage your client relationships
            </Text>
          </View>
          {hasPermission('manage_clients') && (
            <Button
              title="Add Client"
              onPress={handleAddClient}
              size="small"
            />
          )}
        </View>
      </View>

      <View style={styles.content}>
        <FlatList
          data={clients}
          renderItem={renderClientItem}
          keyExtractor={(item) => item.id}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>
                No clients found.{'\n'}
                {hasPermission('manage_clients')
                  ? 'Add your first client to get started!'
                  : 'Contact your administrator to add clients.'}
              </Text>
            </View>
          }
          showsVerticalScrollIndicator={false}
        />
      </View>

      <Modal
        visible={showModal}
        onClose={handleFormCancel}
        title={selectedClient ? 'Edit Client' : 'Add New Client'}
        size="large"
      >
        <ClientForm
          client={selectedClient}
          onSuccess={handleFormSuccess}
          onCancel={handleFormCancel}
        />
      </Modal>
    </SafeAreaView>
  );
}
