// utils/api.ts

import {
    ApiResponse,
    Client,
    Estimate,
    Template
} from '@/types/models';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage keys
const STORAGE_KEYS = {
  CLIENTS: '@construction_app_clients',
  ESTIMATES: '@construction_app_estimates',
  TEMPLATES: '@construction_app_templates',
  ACTIVITIES: '@construction_app_activities',
};

// Mock data
const MOCK_CLIENTS: Client[] = [
  {
    id: '1',
    name: 'ABC Construction Corp',
    email: '<EMAIL>',
    phone: '(*************',
    address: '123 Business Ave, City, State 12345',
    status: 'active',
    projects: ['1', '2'],
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
  {
    id: '2',
    name: 'Smith Residential',
    email: '<EMAIL>',
    phone: '(*************',
    address: '456 Home St, City, State 12345',
    status: 'prospect',
    projects: [],
    createdAt: '2024-01-20T14:30:00Z',
    updatedAt: '2024-01-20T14:30:00Z',
  },
];

const MOCK_ESTIMATES: Estimate[] = [
  {
    id: '1',
    clientId: '1',
    projectName: 'Office Building Renovation',
    projectDescription: 'Complete renovation of 3-story office building including electrical, plumbing, and HVAC updates.',
    startDate: '2024-03-01',
    endDate: '2024-06-30',
    items: [
      {
        id: '1',
        description: 'Electrical wiring upgrade',
        quantity: 1,
        unitPrice: 15000,
        total: 15000,
        category: 'electrical',
      },
      {
        id: '2',
        description: 'HVAC system installation',
        quantity: 1,
        unitPrice: 25000,
        total: 25000,
        category: 'hvac',
      },
    ],
    subtotal: 40000,
    taxes: 3200,
    discount: 0,
    grandTotal: 43200,
    status: 'finalized',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    createdBy: '1',
  },
];

const MOCK_TEMPLATES: Template[] = [
  {
    id: '1',
    name: 'Basic Electrical Work',
    category: 'electrical',
    description: 'Standard electrical installation template',
    defaultItems: [
      {
        id: '1',
        description: 'Electrical outlet installation',
        quantity: 10,
        unitPrice: 50,
        total: 500,
        category: 'electrical',
      },
      {
        id: '2',
        description: 'Light fixture installation',
        quantity: 5,
        unitPrice: 100,
        total: 500,
        category: 'electrical',
      },
    ],
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-10T09:00:00Z',
    createdBy: '1',
  },
];

// Utility functions
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const loadFromStorage = async <T>(key: string, defaultValue: T[]): Promise<T[]> => {
  try {
    const stored = await AsyncStorage.getItem(key);
    return stored ? JSON.parse(stored) : defaultValue;
  } catch (error) {
    console.error(`Error loading ${key}:`, error);
    return defaultValue;
  }
};

const saveToStorage = async <T>(key: string, data: T[]): Promise<void> => {
  try {
    await AsyncStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    console.error(`Error saving ${key}:`, error);
  }
};

// Initialize mock data
export const initializeMockData = async (): Promise<void> => {
  const clients = await loadFromStorage(STORAGE_KEYS.CLIENTS, []);
  const estimates = await loadFromStorage(STORAGE_KEYS.ESTIMATES, []);
  const templates = await loadFromStorage(STORAGE_KEYS.TEMPLATES, []);

  if (clients.length === 0) {
    await saveToStorage(STORAGE_KEYS.CLIENTS, MOCK_CLIENTS);
  }
  if (estimates.length === 0) {
    await saveToStorage(STORAGE_KEYS.ESTIMATES, MOCK_ESTIMATES);
  }
  if (templates.length === 0) {
    await saveToStorage(STORAGE_KEYS.TEMPLATES, MOCK_TEMPLATES);
  }
};

// Client API
export const clientApi = {
  getAll: async (): Promise<ApiResponse<Client[]>> => {
    await delay(500);
    try {
      const clients = await loadFromStorage(STORAGE_KEYS.CLIENTS, MOCK_CLIENTS);
      return { success: true, data: clients };
    } catch (error) {
      return { success: false, error: 'Failed to load clients' };
    }
  },

  getById: async (id: string): Promise<ApiResponse<Client>> => {
    await delay(300);
    try {
      const clients = await loadFromStorage(STORAGE_KEYS.CLIENTS, MOCK_CLIENTS);
      const client = clients.find(c => c.id === id);
      if (!client) {
        return { success: false, error: 'Client not found' };
      }
      return { success: true, data: client };
    } catch (error) {
      return { success: false, error: 'Failed to load client' };
    }
  },

  create: async (clientData: Omit<Client, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Client>> => {
    await delay(800);
    try {
      const clients = await loadFromStorage(STORAGE_KEYS.CLIENTS, MOCK_CLIENTS);
      const newClient: Client = {
        ...clientData,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      clients.push(newClient);
      await saveToStorage(STORAGE_KEYS.CLIENTS, clients);
      return { success: true, data: newClient };
    } catch (error) {
      return { success: false, error: 'Failed to create client' };
    }
  },

  update: async (id: string, clientData: Partial<Client>): Promise<ApiResponse<Client>> => {
    await delay(600);
    try {
      const clients = await loadFromStorage(STORAGE_KEYS.CLIENTS, MOCK_CLIENTS);
      const index = clients.findIndex(c => c.id === id);
      if (index === -1) {
        return { success: false, error: 'Client not found' };
      }
      clients[index] = {
        ...clients[index],
        ...clientData,
        updatedAt: new Date().toISOString(),
      };
      await saveToStorage(STORAGE_KEYS.CLIENTS, clients);
      return { success: true, data: clients[index] };
    } catch (error) {
      return { success: false, error: 'Failed to update client' };
    }
  },

  delete: async (id: string): Promise<ApiResponse<void>> => {
    await delay(400);
    try {
      const clients = await loadFromStorage(STORAGE_KEYS.CLIENTS, MOCK_CLIENTS);
      const filteredClients = clients.filter(c => c.id !== id);
      await saveToStorage(STORAGE_KEYS.CLIENTS, filteredClients);
      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to delete client' };
    }
  },
};

// Estimate API
export const estimateApi = {
  getAll: async (): Promise<ApiResponse<Estimate[]>> => {
    await delay(500);
    try {
      const estimates = await loadFromStorage(STORAGE_KEYS.ESTIMATES, MOCK_ESTIMATES);
      return { success: true, data: estimates };
    } catch (error) {
      return { success: false, error: 'Failed to load estimates' };
    }
  },

  getById: async (id: string): Promise<ApiResponse<Estimate>> => {
    await delay(300);
    try {
      const estimates = await loadFromStorage(STORAGE_KEYS.ESTIMATES, MOCK_ESTIMATES);
      const estimate = estimates.find(e => e.id === id);
      if (!estimate) {
        return { success: false, error: 'Estimate not found' };
      }
      return { success: true, data: estimate };
    } catch (error) {
      return { success: false, error: 'Failed to load estimate' };
    }
  },

  create: async (estimateData: Omit<Estimate, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Estimate>> => {
    await delay(800);
    try {
      const estimates = await loadFromStorage(STORAGE_KEYS.ESTIMATES, MOCK_ESTIMATES);
      const newEstimate: Estimate = {
        ...estimateData,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      estimates.push(newEstimate);
      await saveToStorage(STORAGE_KEYS.ESTIMATES, estimates);
      return { success: true, data: newEstimate };
    } catch (error) {
      return { success: false, error: 'Failed to create estimate' };
    }
  },

  update: async (id: string, estimateData: Partial<Estimate>): Promise<ApiResponse<Estimate>> => {
    await delay(600);
    try {
      const estimates = await loadFromStorage(STORAGE_KEYS.ESTIMATES, MOCK_ESTIMATES);
      const index = estimates.findIndex(e => e.id === id);
      if (index === -1) {
        return { success: false, error: 'Estimate not found' };
      }
      estimates[index] = {
        ...estimates[index],
        ...estimateData,
        updatedAt: new Date().toISOString(),
      };
      await saveToStorage(STORAGE_KEYS.ESTIMATES, estimates);
      return { success: true, data: estimates[index] };
    } catch (error) {
      return { success: false, error: 'Failed to update estimate' };
    }
  },

  delete: async (id: string): Promise<ApiResponse<void>> => {
    await delay(400);
    try {
      const estimates = await loadFromStorage(STORAGE_KEYS.ESTIMATES, MOCK_ESTIMATES);
      const filteredEstimates = estimates.filter(e => e.id !== id);
      await saveToStorage(STORAGE_KEYS.ESTIMATES, filteredEstimates);
      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to delete estimate' };
    }
  },
};

// Template API
export const templateApi = {
  getAll: async (): Promise<ApiResponse<Template[]>> => {
    await delay(400);
    try {
      const templates = await loadFromStorage(STORAGE_KEYS.TEMPLATES, MOCK_TEMPLATES);
      return { success: true, data: templates };
    } catch (error) {
      return { success: false, error: 'Failed to load templates' };
    }
  },

  getById: async (id: string): Promise<ApiResponse<Template>> => {
    await delay(300);
    try {
      const templates = await loadFromStorage(STORAGE_KEYS.TEMPLATES, MOCK_TEMPLATES);
      const template = templates.find(t => t.id === id);
      if (!template) {
        return { success: false, error: 'Template not found' };
      }
      return { success: true, data: template };
    } catch (error) {
      return { success: false, error: 'Failed to load template' };
    }
  },

  create: async (templateData: Omit<Template, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Template>> => {
    await delay(600);
    try {
      const templates = await loadFromStorage(STORAGE_KEYS.TEMPLATES, MOCK_TEMPLATES);
      const newTemplate: Template = {
        ...templateData,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      templates.push(newTemplate);
      await saveToStorage(STORAGE_KEYS.TEMPLATES, templates);
      return { success: true, data: newTemplate };
    } catch (error) {
      return { success: false, error: 'Failed to create template' };
    }
  },

  update: async (id: string, templateData: Partial<Template>): Promise<ApiResponse<Template>> => {
    await delay(500);
    try {
      const templates = await loadFromStorage(STORAGE_KEYS.TEMPLATES, MOCK_TEMPLATES);
      const index = templates.findIndex(t => t.id === id);
      if (index === -1) {
        return { success: false, error: 'Template not found' };
      }
      templates[index] = {
        ...templates[index],
        ...templateData,
        updatedAt: new Date().toISOString(),
      };
      await saveToStorage(STORAGE_KEYS.TEMPLATES, templates);
      return { success: true, data: templates[index] };
    } catch (error) {
      return { success: false, error: 'Failed to update template' };
    }
  },

  delete: async (id: string): Promise<ApiResponse<void>> => {
    await delay(300);
    try {
      const templates = await loadFromStorage(STORAGE_KEYS.TEMPLATES, MOCK_TEMPLATES);
      const filteredTemplates = templates.filter(t => t.id !== id);
      await saveToStorage(STORAGE_KEYS.TEMPLATES, filteredTemplates);
      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to delete template' };
    }
  },
};

// Dashboard API
export const dashboardApi = {
  getStats: async (): Promise<ApiResponse<DashboardStats>> => {
    await delay(600);
    try {
      const estimates = await loadFromStorage(STORAGE_KEYS.ESTIMATES, MOCK_ESTIMATES);
      const clients = await loadFromStorage(STORAGE_KEYS.CLIENTS, MOCK_CLIENTS);

      const totalEstimates = estimates.length;
      const pendingEstimates = estimates.filter(e => e.status === 'draft' || e.status === 'sent').length;
      const totalRevenue = estimates
        .filter(e => e.status === 'approved')
        .reduce((sum, e) => sum + e.grandTotal, 0);
      const activeProjects = estimates.filter(e => e.status !== 'rejected').length;

      // Mock recent activity
      const recentActivity: ActivityItem[] = [
        {
          id: '1',
          type: 'estimate_created',
          description: 'New estimate created for ABC Construction Corp',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          userId: '1',
        },
        {
          id: '2',
          type: 'client_added',
          description: 'New client Smith Residential added',
          timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
          userId: '1',
        },
        {
          id: '3',
          type: 'estimate_updated',
          description: 'Estimate EST-240115-001 updated',
          timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          userId: '2',
        },
      ];

      const stats: DashboardStats = {
        totalEstimates,
        pendingEstimates,
        totalRevenue,
        activeProjects,
        recentActivity,
      };

      return { success: true, data: stats };
    } catch (error) {
      return { success: false, error: 'Failed to load dashboard stats' };
    }
  },
};