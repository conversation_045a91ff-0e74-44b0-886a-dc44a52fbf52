// utils/calculations.ts

import { EstimateItem, Estimate } from '@/types/models';

export interface EstimateCalculation {
  subtotal: number;
  taxes: number;
  discount: number;
  grandTotal: number;
}

export const calculateItemTotal = (quantity: number, unitPrice: number): number => {
  return Math.round((quantity * unitPrice) * 100) / 100;
};

export const calculateSubtotal = (items: EstimateItem[]): number => {
  const subtotal = items.reduce((sum, item) => sum + item.total, 0);
  return Math.round(subtotal * 100) / 100;
};

export const calculateTaxes = (subtotal: number, taxRate: number): number => {
  const taxes = subtotal * taxRate;
  return Math.round(taxes * 100) / 100;
};

export const calculateDiscount = (subtotal: number, discountPercent: number): number => {
  const discount = subtotal * (discountPercent / 100);
  return Math.round(discount * 100) / 100;
};

export const calculateGrandTotal = (
  subtotal: number,
  taxes: number,
  discount: number
): number => {
  const total = subtotal + taxes - discount;
  return Math.round(total * 100) / 100;
};

export const calculateEstimateTotal = (
  items: EstimateItem[],
  taxRate: number = 0,
  discountPercent: number = 0
): EstimateCalculation => {
  const subtotal = calculateSubtotal(items);
  const taxes = calculateTaxes(subtotal, taxRate);
  const discount = calculateDiscount(subtotal, discountPercent);
  const grandTotal = calculateGrandTotal(subtotal, taxes, discount);

  return {
    subtotal,
    taxes,
    discount,
    grandTotal,
  };
};

export const updateEstimateCalculations = (
  estimate: Estimate,
  taxRate: number = 0.08,
  discountPercent: number = 0
): Estimate => {
  const calculations = calculateEstimateTotal(estimate.items, taxRate, discountPercent);
  
  return {
    ...estimate,
    ...calculations,
  };
};

export const formatCurrency = (
  amount: number,
  currency: string = 'USD',
  locale: string = 'en-US'
): string => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(amount);
};

export const formatNumber = (
  value: number,
  decimals: number = 2,
  locale: string = 'en-US'
): string => {
  return new Intl.NumberFormat(locale, {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value);
};

export const formatPercentage = (
  value: number,
  decimals: number = 1,
  locale: string = 'en-US'
): string => {
  return new Intl.NumberFormat(locale, {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value);
};

export const parseCurrency = (value: string): number => {
  // Remove currency symbols and parse as float
  const cleaned = value.replace(/[^0-9.-]+/g, '');
  const parsed = parseFloat(cleaned);
  return isNaN(parsed) ? 0 : parsed;
};

export const calculateProfitMargin = (cost: number, price: number): number => {
  if (price === 0) return 0;
  return ((price - cost) / price) * 100;
};

export const calculateMarkup = (cost: number, price: number): number => {
  if (cost === 0) return 0;
  return ((price - cost) / cost) * 100;
};

export const applyMarkup = (cost: number, markupPercent: number): number => {
  return cost * (1 + markupPercent / 100);
};

export const calculateEstimateDuration = (startDate: string, endDate: string): number => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const diffTime = Math.abs(end.getTime() - start.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
};

export const calculateProjectProgress = (
  startDate: string,
  endDate: string,
  currentDate: string = new Date().toISOString()
): number => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const current = new Date(currentDate);

  if (current <= start) return 0;
  if (current >= end) return 100;

  const totalDuration = end.getTime() - start.getTime();
  const elapsed = current.getTime() - start.getTime();
  
  return Math.round((elapsed / totalDuration) * 100);
};

export const generateEstimateNumber = (prefix: string = 'EST'): string => {
  const date = new Date();
  const year = date.getFullYear().toString().slice(-2);
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  
  return `${prefix}-${year}${month}${day}-${random}`;
};
