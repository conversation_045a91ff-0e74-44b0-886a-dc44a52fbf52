// components/forms/ClientForm.tsx

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';

import { useTheme } from '@/hooks/useTheme';
import { useForm } from '@/hooks/useForm';
import { Client } from '@/types/models';
import { CLIENT_VALIDATION_SCHEMA } from '@/utils/validation';
import { clientApi } from '@/utils/api';
import { CLIENT_STATUSES } from '@/constants/categories';
import { Spacing, FontSizes, FontWeights } from '@/constants/Colors';

import Input from '@/components/ui/Input';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';

interface ClientFormData {
  name: string;
  email: string;
  phone: string;
  address: string;
  status: 'active' | 'inactive' | 'prospect';
}

interface ClientFormProps {
  client?: Client;
  onSuccess: (client: Client) => void;
  onCancel: () => void;
}

export default function ClientForm({ client, onSuccess, onCancel }: ClientFormProps) {
  const { colors } = useTheme();
  const isEditing = !!client;

  const {
    values,
    errors,
    isSubmitting,
    setValue,
    getFieldError,
    hasFieldError,
    handleSubmit,
  } = useForm<ClientFormData>({
    initialValues: {
      name: client?.name || '',
      email: client?.email || '',
      phone: client?.phone || '',
      address: client?.address || '',
      status: client?.status || 'prospect',
    },
    validationSchema: CLIENT_VALIDATION_SCHEMA,
    onSubmit: async (formData) => {
      try {
        let result;
        if (isEditing && client) {
          result = await clientApi.update(client.id, formData);
        } else {
          result = await clientApi.create({
            ...formData,
            projects: [],
          });
        }

        if (result.success && result.data) {
          Alert.alert(
            'Success',
            `Client ${isEditing ? 'updated' : 'created'} successfully!`,
            [
              {
                text: 'OK',
                onPress: () => onSuccess(result.data!),
              },
            ]
          );
        } else {
          Alert.alert('Error', result.error || 'Failed to save client');
        }
      } catch (error) {
        console.error('Client form error:', error);
        Alert.alert('Error', 'An unexpected error occurred');
      }
    },
  });

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    header: {
      marginBottom: Spacing.lg,
    },
    title: {
      fontSize: FontSizes.xl,
      fontWeight: FontWeights.bold,
      color: colors.text,
      marginBottom: Spacing.xs,
    },
    subtitle: {
      fontSize: FontSizes.md,
      color: colors.textSecondary,
    },
    form: {
      flex: 1,
    },
    section: {
      marginBottom: Spacing.xl,
    },
    sectionTitle: {
      fontSize: FontSizes.lg,
      fontWeight: FontWeights.semibold,
      color: colors.text,
      marginBottom: Spacing.md,
    },
    statusSelector: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: Spacing.sm,
      marginBottom: Spacing.md,
    },
    statusOption: {
      flex: 1,
      minWidth: '30%',
      backgroundColor: colors.surface,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 8,
      padding: Spacing.md,
      alignItems: 'center',
    },
    statusOptionSelected: {
      backgroundColor: colors.primary,
      borderColor: colors.primary,
    },
    statusOptionText: {
      fontSize: FontSizes.sm,
      fontWeight: FontWeights.medium,
      color: colors.text,
      textAlign: 'center',
    },
    statusOptionTextSelected: {
      color: '#FFFFFF',
    },
    buttonContainer: {
      flexDirection: 'row',
      gap: Spacing.md,
      marginTop: Spacing.lg,
    },
    button: {
      flex: 1,
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>
          {isEditing ? 'Edit Client' : 'Add New Client'}
        </Text>
        <Text style={styles.subtitle}>
          {isEditing 
            ? 'Update client information and details'
            : 'Enter client information to create a new client record'
          }
        </Text>
      </View>

      <ScrollView style={styles.form} showsVerticalScrollIndicator={false}>
        <Card>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Basic Information</Text>
            
            <Input
              label="Client Name"
              value={values.name}
              onChangeText={(text) => setValue('name', text)}
              placeholder="Enter client or company name"
              error={getFieldError('name')}
              required
            />

            <Input
              label="Email Address"
              value={values.email}
              onChangeText={(text) => setValue('email', text)}
              placeholder="Enter email address"
              keyboardType="email-address"
              autoCapitalize="none"
              error={getFieldError('email')}
              required
            />

            <Input
              label="Phone Number"
              value={values.phone}
              onChangeText={(text) => setValue('phone', text)}
              placeholder="Enter phone number"
              keyboardType="phone-pad"
              error={getFieldError('phone')}
              required
            />

            <Input
              label="Address"
              value={values.address}
              onChangeText={(text) => setValue('address', text)}
              placeholder="Enter full address"
              multiline
              numberOfLines={3}
              error={getFieldError('address')}
              required
            />
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Status</Text>
            <View style={styles.statusSelector}>
              {CLIENT_STATUSES.map((status) => (
                <Card
                  key={status.value}
                  style={[
                    styles.statusOption,
                    values.status === status.value && styles.statusOptionSelected,
                  ]}
                  onPress={() => setValue('status', status.value as any)}
                  shadow="none"
                  padding="none"
                >
                  <Text
                    style={[
                      styles.statusOptionText,
                      values.status === status.value && styles.statusOptionTextSelected,
                    ]}
                  >
                    {status.label}
                  </Text>
                </Card>
              ))}
            </View>
          </View>
        </Card>

        <View style={styles.buttonContainer}>
          <Button
            title="Cancel"
            onPress={onCancel}
            variant="outline"
            style={styles.button}
          />
          <Button
            title={isEditing ? 'Update Client' : 'Create Client'}
            onPress={handleSubmit}
            loading={isSubmitting}
            style={styles.button}
          />
        </View>
      </ScrollView>
    </View>
  );
}
