// app/(tabs)/settings.tsx

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { useTheme } from '@/hooks/useTheme';
import { useAuth } from '@/hooks/useAuth';
import { useApp } from '@/context/AppContext';
import { Spacing, FontSizes, FontWeights } from '@/constants/Colors';

export default function SettingsScreen() {
  const { colors, theme, setTheme } = useTheme();
  const { user, logout } = useAuth();
  const { settings, updateSettings } = useApp();

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Logout', 
          style: 'destructive',
          onPress: logout 
        },
      ]
    );
  };

  const handleThemeChange = (newTheme: 'light' | 'dark' | 'system') => {
    setTheme(newTheme);
  };

  const handleNotificationToggle = (value: boolean) => {
    updateSettings({ notifications: value });
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      padding: Spacing.lg,
      backgroundColor: colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    title: {
      fontSize: FontSizes.xxl,
      fontWeight: FontWeights.bold,
      color: colors.text,
      marginBottom: Spacing.xs,
    },
    subtitle: {
      fontSize: FontSizes.md,
      color: colors.textSecondary,
    },
    content: {
      flex: 1,
      padding: Spacing.lg,
    },
    section: {
      marginBottom: Spacing.xl,
    },
    sectionTitle: {
      fontSize: FontSizes.lg,
      fontWeight: FontWeights.semibold,
      color: colors.text,
      marginBottom: Spacing.md,
    },
    settingItem: {
      backgroundColor: colors.card,
      padding: Spacing.lg,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: colors.border,
      marginBottom: Spacing.sm,
    },
    settingRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    settingLabel: {
      fontSize: FontSizes.md,
      fontWeight: FontWeights.medium,
      color: colors.text,
      flex: 1,
    },
    settingDescription: {
      fontSize: FontSizes.sm,
      color: colors.textSecondary,
      marginTop: Spacing.xs,
    },
    themeOptions: {
      flexDirection: 'row',
      marginTop: Spacing.sm,
    },
    themeOption: {
      flex: 1,
      padding: Spacing.sm,
      marginHorizontal: Spacing.xs,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: colors.border,
      alignItems: 'center',
    },
    themeOptionActive: {
      backgroundColor: colors.primary,
      borderColor: colors.primary,
    },
    themeOptionText: {
      fontSize: FontSizes.sm,
      color: colors.text,
    },
    themeOptionTextActive: {
      color: '#FFFFFF',
    },
    userInfo: {
      backgroundColor: colors.card,
      padding: Spacing.lg,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: colors.border,
      marginBottom: Spacing.md,
    },
    userName: {
      fontSize: FontSizes.lg,
      fontWeight: FontWeights.semibold,
      color: colors.text,
      marginBottom: Spacing.xs,
    },
    userEmail: {
      fontSize: FontSizes.md,
      color: colors.textSecondary,
      marginBottom: Spacing.xs,
    },
    userRole: {
      fontSize: FontSizes.sm,
      color: colors.primary,
      fontWeight: FontWeights.medium,
    },
    logoutButton: {
      backgroundColor: colors.error,
      padding: Spacing.lg,
      borderRadius: 12,
      alignItems: 'center',
      marginTop: Spacing.lg,
    },
    logoutButtonText: {
      fontSize: FontSizes.md,
      fontWeight: FontWeights.semibold,
      color: '#FFFFFF',
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Settings</Text>
        <Text style={styles.subtitle}>
          Manage your app preferences and account
        </Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* User Profile Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Profile</Text>
          <View style={styles.userInfo}>
            <Text style={styles.userName}>{user?.name}</Text>
            <Text style={styles.userEmail}>{user?.email}</Text>
            <Text style={styles.userRole}>
              {user?.role.charAt(0).toUpperCase() + user?.role.slice(1)}
            </Text>
          </View>
        </View>

        {/* Appearance Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Appearance</Text>
          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Theme</Text>
            <Text style={styles.settingDescription}>
              Choose your preferred app theme
            </Text>
            <View style={styles.themeOptions}>
              {(['light', 'dark', 'system'] as const).map((themeOption) => (
                <TouchableOpacity
                  key={themeOption}
                  style={[
                    styles.themeOption,
                    theme === themeOption && styles.themeOptionActive,
                  ]}
                  onPress={() => handleThemeChange(themeOption)}
                >
                  <Text
                    style={[
                      styles.themeOptionText,
                      theme === themeOption && styles.themeOptionTextActive,
                    ]}
                  >
                    {themeOption.charAt(0).toUpperCase() + themeOption.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>

        {/* Notifications Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notifications</Text>
          <View style={styles.settingItem}>
            <View style={styles.settingRow}>
              <View style={{ flex: 1 }}>
                <Text style={styles.settingLabel}>Push Notifications</Text>
                <Text style={styles.settingDescription}>
                  Receive notifications about estimates and updates
                </Text>
              </View>
              <Switch
                value={settings.notifications}
                onValueChange={handleNotificationToggle}
                trackColor={{ false: colors.border, true: colors.primary }}
                thumbColor={colors.card}
              />
            </View>
          </View>
        </View>

        {/* Company Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Company</Text>
          <TouchableOpacity style={styles.settingItem}>
            <Text style={styles.settingLabel}>Company Information</Text>
            <Text style={styles.settingDescription}>
              Update your company details and branding
            </Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.settingItem}>
            <Text style={styles.settingLabel}>Tax Settings</Text>
            <Text style={styles.settingDescription}>
              Configure default tax rates and currency
            </Text>
          </TouchableOpacity>
        </View>

        {/* Data Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Data</Text>
          <TouchableOpacity style={styles.settingItem}>
            <Text style={styles.settingLabel}>Export Data</Text>
            <Text style={styles.settingDescription}>
              Export your estimates and client data
            </Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.settingItem}>
            <Text style={styles.settingLabel}>Backup & Sync</Text>
            <Text style={styles.settingDescription}>
              Manage data backup and synchronization
            </Text>
          </TouchableOpacity>
        </View>

        {/* Logout Button */}
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Text style={styles.logoutButtonText}>Logout</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}
