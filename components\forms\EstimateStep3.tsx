// components/forms/EstimateStep3.tsx

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';

import { useTheme } from '@/hooks/useTheme';
import { Client } from '@/types/models';
import { calculateEstimateTotal, formatCurrency } from '@/utils/calculations';
import { TRADE_CATEGORIES } from '@/constants/categories';
import { Spacing, FontSizes, FontWeights } from '@/constants/Colors';

import Input from '@/components/ui/Input';
import Card from '@/components/ui/Card';
import { EstimateFormData } from './EstimateWizard';

interface EstimateStep3Props {
  formData: EstimateFormData;
  updateFormData: (updates: Partial<EstimateFormData>) => void;
  clients: Client[];
}

export default function EstimateStep3({ formData, updateFormData, clients }: EstimateStep3Props) {
  const { colors } = useTheme();

  const selectedClient = clients.find(c => c.id === formData.clientId);
  const calculations = calculateEstimateTotal(formData.items, formData.taxRate, formData.discountPercent);

  const getCategoryName = (categoryId: string) => {
    const category = TRADE_CATEGORIES.find(c => c.id === categoryId);
    return category?.name || 'General';
  };

  const styles = StyleSheet.create({
    container: {
      padding: Spacing.lg,
    },
    section: {
      marginBottom: Spacing.xl,
    },
    sectionTitle: {
      fontSize: FontSizes.lg,
      fontWeight: FontWeights.semibold,
      color: colors.text,
      marginBottom: Spacing.md,
    },
    summaryCard: {
      backgroundColor: colors.surface,
      padding: Spacing.lg,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: colors.border,
      marginBottom: Spacing.md,
    },
    summaryRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: Spacing.sm,
    },
    summaryLabel: {
      fontSize: FontSizes.md,
      color: colors.textSecondary,
    },
    summaryValue: {
      fontSize: FontSizes.md,
      fontWeight: FontWeights.medium,
      color: colors.text,
    },
    clientInfo: {
      marginBottom: Spacing.md,
    },
    clientName: {
      fontSize: FontSizes.lg,
      fontWeight: FontWeights.semibold,
      color: colors.text,
      marginBottom: Spacing.xs,
    },
    clientDetails: {
      fontSize: FontSizes.sm,
      color: colors.textSecondary,
      marginBottom: Spacing.xs,
    },
    projectInfo: {
      marginBottom: Spacing.md,
    },
    projectName: {
      fontSize: FontSizes.lg,
      fontWeight: FontWeights.semibold,
      color: colors.text,
      marginBottom: Spacing.xs,
    },
    projectDescription: {
      fontSize: FontSizes.md,
      color: colors.textSecondary,
      marginBottom: Spacing.sm,
    },
    dateRange: {
      fontSize: FontSizes.sm,
      color: colors.textSecondary,
    },
    itemsList: {
      marginBottom: Spacing.lg,
    },
    itemCard: {
      backgroundColor: colors.surface,
      padding: Spacing.md,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: colors.border,
      marginBottom: Spacing.sm,
    },
    itemDescription: {
      fontSize: FontSizes.md,
      fontWeight: FontWeights.medium,
      color: colors.text,
      marginBottom: Spacing.xs,
    },
    itemDetails: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    itemInfo: {
      flex: 1,
    },
    itemQuantity: {
      fontSize: FontSizes.sm,
      color: colors.textSecondary,
    },
    itemCategory: {
      fontSize: FontSizes.sm,
      color: colors.textSecondary,
    },
    itemTotal: {
      fontSize: FontSizes.lg,
      fontWeight: FontWeights.semibold,
      color: colors.primary,
    },
    calculationsCard: {
      backgroundColor: colors.primary,
      padding: Spacing.lg,
      borderRadius: 8,
    },
    calculationRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: Spacing.sm,
    },
    calculationLabel: {
      fontSize: FontSizes.md,
      color: '#FFFFFF',
      opacity: 0.9,
    },
    calculationValue: {
      fontSize: FontSizes.md,
      fontWeight: FontWeights.medium,
      color: '#FFFFFF',
    },
    grandTotalRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingTop: Spacing.sm,
      borderTopWidth: 1,
      borderTopColor: '#FFFFFF',
      borderTopOpacity: 0.3,
    },
    grandTotalLabel: {
      fontSize: FontSizes.lg,
      fontWeight: FontWeights.semibold,
      color: '#FFFFFF',
    },
    grandTotalValue: {
      fontSize: FontSizes.xl,
      fontWeight: FontWeights.bold,
      color: '#FFFFFF',
    },
    adjustmentsRow: {
      flexDirection: 'row',
      gap: Spacing.md,
    },
    adjustmentInput: {
      flex: 1,
    },
  });

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Client Information</Text>
        <Card padding="medium">
          <View style={styles.clientInfo}>
            <Text style={styles.clientName}>{selectedClient?.name}</Text>
            <Text style={styles.clientDetails}>{selectedClient?.email}</Text>
            <Text style={styles.clientDetails}>{selectedClient?.phone}</Text>
            <Text style={styles.clientDetails}>{selectedClient?.address}</Text>
          </View>
        </Card>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Project Details</Text>
        <Card padding="medium">
          <View style={styles.projectInfo}>
            <Text style={styles.projectName}>{formData.projectName}</Text>
            <Text style={styles.projectDescription}>{formData.projectDescription}</Text>
            <Text style={styles.dateRange}>
              {new Date(formData.startDate).toLocaleDateString()} - {new Date(formData.endDate).toLocaleDateString()}
            </Text>
          </View>
        </Card>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Items ({formData.items.length})</Text>
        <View style={styles.itemsList}>
          {formData.items.map((item) => (
            <View key={item.id} style={styles.itemCard}>
              <Text style={styles.itemDescription}>{item.description}</Text>
              <View style={styles.itemDetails}>
                <View style={styles.itemInfo}>
                  <Text style={styles.itemQuantity}>
                    Qty: {item.quantity} × {formatCurrency(item.unitPrice)}
                  </Text>
                  <Text style={styles.itemCategory}>
                    Category: {getCategoryName(item.category || 'general')}
                  </Text>
                </View>
                <Text style={styles.itemTotal}>{formatCurrency(item.total)}</Text>
              </View>
            </View>
          ))}
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Adjustments</Text>
        <Card padding="medium">
          <View style={styles.adjustmentsRow}>
            <Input
              label="Tax Rate (%)"
              value={(formData.taxRate * 100).toString()}
              onChangeText={(text) => {
                const rate = parseFloat(text) || 0;
                updateFormData({ taxRate: rate / 100 });
              }}
              placeholder="8.0"
              keyboardType="numeric"
              containerStyle={styles.adjustmentInput}
            />
            <Input
              label="Discount (%)"
              value={formData.discountPercent.toString()}
              onChangeText={(text) => {
                const discount = parseFloat(text) || 0;
                updateFormData({ discountPercent: discount });
              }}
              placeholder="0.0"
              keyboardType="numeric"
              containerStyle={styles.adjustmentInput}
            />
          </View>
        </Card>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Additional Notes</Text>
        <Input
          value={formData.notes}
          onChangeText={(text) => updateFormData({ notes: text })}
          placeholder="Add any additional notes or terms for this estimate..."
          multiline
          numberOfLines={4}
        />
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Total Calculation</Text>
        <View style={styles.calculationsCard}>
          <View style={styles.calculationRow}>
            <Text style={styles.calculationLabel}>Subtotal</Text>
            <Text style={styles.calculationValue}>{formatCurrency(calculations.subtotal)}</Text>
          </View>
          
          {calculations.taxes > 0 && (
            <View style={styles.calculationRow}>
              <Text style={styles.calculationLabel}>
                Tax ({(formData.taxRate * 100).toFixed(1)}%)
              </Text>
              <Text style={styles.calculationValue}>{formatCurrency(calculations.taxes)}</Text>
            </View>
          )}
          
          {calculations.discount > 0 && (
            <View style={styles.calculationRow}>
              <Text style={styles.calculationLabel}>
                Discount ({formData.discountPercent.toFixed(1)}%)
              </Text>
              <Text style={styles.calculationValue}>-{formatCurrency(calculations.discount)}</Text>
            </View>
          )}
          
          <View style={styles.grandTotalRow}>
            <Text style={styles.grandTotalLabel}>Grand Total</Text>
            <Text style={styles.grandTotalValue}>{formatCurrency(calculations.grandTotal)}</Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
}
