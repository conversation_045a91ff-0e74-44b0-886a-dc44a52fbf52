// hooks/useForm.ts

import { useState, useCallback } from 'react';
import { FormState, ValidationError, ValidationSchema, validateForm } from '@/utils/validation';

interface UseFormOptions<T> {
  initialValues: T;
  validationSchema?: ValidationSchema;
  onSubmit?: (values: T) => Promise<void> | void;
}

interface UseFormReturn<T> {
  values: T;
  errors: ValidationError[];
  isValid: boolean;
  isSubmitting: boolean;
  setValue: (field: keyof T, value: any) => void;
  setValues: (values: Partial<T>) => void;
  setError: (field: string, message: string) => void;
  clearErrors: () => void;
  handleSubmit: () => Promise<void>;
  reset: () => void;
  getFieldError: (field: keyof T) => string | undefined;
  hasFieldError: (field: keyof T) => boolean;
}

export function useForm<T extends Record<string, any>>({
  initialValues,
  validationSchema,
  onSubmit,
}: UseFormOptions<T>): UseFormReturn<T> {
  const [values, setValuesState] = useState<T>(initialValues);
  const [errors, setErrors] = useState<ValidationError[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const setValue = useCallback((field: keyof T, value: any) => {
    setValuesState(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when value changes
    setErrors(prev => prev.filter(error => error.field !== field));
  }, []);

  const setValues = useCallback((newValues: Partial<T>) => {
    setValuesState(prev => ({ ...prev, ...newValues }));
  }, []);

  const setError = useCallback((field: string, message: string) => {
    setErrors(prev => {
      const filtered = prev.filter(error => error.field !== field);
      return [...filtered, { field, message }];
    });
  }, []);

  const clearErrors = useCallback(() => {
    setErrors([]);
  }, []);

  const validate = useCallback((): boolean => {
    if (!validationSchema) return true;

    const validationErrors = validateForm(values, validationSchema);
    setErrors(validationErrors);
    return validationErrors.length === 0;
  }, [values, validationSchema]);

  const handleSubmit = useCallback(async () => {
    if (!onSubmit) return;

    const isFormValid = validate();
    if (!isFormValid) return;

    setIsSubmitting(true);
    try {
      await onSubmit(values);
    } catch (error) {
      console.error('Form submission error:', error);
      setError('submit', 'An error occurred while submitting the form');
    } finally {
      setIsSubmitting(false);
    }
  }, [values, validate, onSubmit]);

  const reset = useCallback(() => {
    setValuesState(initialValues);
    setErrors([]);
    setIsSubmitting(false);
  }, [initialValues]);

  const getFieldError = useCallback((field: keyof T): string | undefined => {
    const error = errors.find(e => e.field === field);
    return error?.message;
  }, [errors]);

  const hasFieldError = useCallback((field: keyof T): boolean => {
    return errors.some(e => e.field === field);
  }, [errors]);

  const isValid = errors.length === 0;

  return {
    values,
    errors,
    isValid,
    isSubmitting,
    setValue,
    setValues,
    setError,
    clearErrors,
    handleSubmit,
    reset,
    getFieldError,
    hasFieldError,
  };
}
