// context/AppContext.tsx

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppSettings, CompanyInfo } from '@/types/models';

interface AppContextType {
  settings: AppSettings;
  updateSettings: (settings: Partial<AppSettings>) => Promise<void>;
  updateCompanyInfo: (companyInfo: Partial<CompanyInfo>) => Promise<void>;
  isLoading: boolean;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

const STORAGE_KEY = '@construction_app_settings';

const DEFAULT_SETTINGS: AppSettings = {
  theme: 'system',
  notifications: true,
  taxRate: 0.08,
  currency: 'USD',
  companyInfo: {
    name: 'Your Construction Company',
    address: '123 Main St, City, State 12345',
    phone: '(*************',
    email: '<EMAIL>',
    website: 'www.yourcompany.com',
  },
};

export function AppProvider({ children }: { children: ReactNode }) {
  const [settings, setSettings] = useState<AppSettings>(DEFAULT_SETTINGS);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadStoredSettings();
  }, []);

  const loadStoredSettings = async () => {
    try {
      const storedSettings = await AsyncStorage.getItem(STORAGE_KEY);
      if (storedSettings) {
        const parsedSettings = JSON.parse(storedSettings);
        setSettings({ ...DEFAULT_SETTINGS, ...parsedSettings });
      }
    } catch (error) {
      console.error('Error loading stored settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateSettings = async (newSettings: Partial<AppSettings>) => {
    try {
      const updatedSettings = { ...settings, ...newSettings };
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updatedSettings));
      setSettings(updatedSettings);
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  };

  const updateCompanyInfo = async (companyInfo: Partial<CompanyInfo>) => {
    try {
      const updatedSettings = {
        ...settings,
        companyInfo: { ...settings.companyInfo, ...companyInfo },
      };
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updatedSettings));
      setSettings(updatedSettings);
    } catch (error) {
      console.error('Error saving company info:', error);
    }
  };

  const value: AppContextType = {
    settings,
    updateSettings,
    updateCompanyInfo,
    isLoading,
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
}

export function useApp() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}
