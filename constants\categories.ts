// constants/categories.ts

export const TRADE_CATEGORIES = [
  {
    id: 'general',
    name: 'General Construction',
    icon: 'hammer',
    color: '#2196F3',
  },
  {
    id: 'electrical',
    name: 'Electrical',
    icon: 'flash',
    color: '#FF9800',
  },
  {
    id: 'plumbing',
    name: 'Plumbing',
    icon: 'water',
    color: '#2196F3',
  },
  {
    id: 'hvac',
    name: 'HVAC',
    icon: 'thermometer',
    color: '#4CAF50',
  },
  {
    id: 'roofing',
    name: 'Roofing',
    icon: 'home',
    color: '#795548',
  },
  {
    id: 'flooring',
    name: 'Flooring',
    icon: 'grid',
    color: '#9C27B0',
  },
  {
    id: 'painting',
    name: 'Painting',
    icon: 'brush',
    color: '#E91E63',
  },
  {
    id: 'landscaping',
    name: 'Landscaping',
    icon: 'leaf',
    color: '#4CAF50',
  },
  {
    id: 'concrete',
    name: 'Concrete',
    icon: 'cube',
    color: '#607D8B',
  },
  {
    id: 'drywall',
    name: 'Drywall',
    icon: 'square',
    color: '#FFC107',
  },
  {
    id: 'insulation',
    name: 'Insulation',
    icon: 'layers',
    color: '#FF5722',
  },
  {
    id: 'windows',
    name: 'Windows & Doors',
    icon: 'window',
    color: '#00BCD4',
  },
];

export const ESTIMATE_STATUSES = [
  {
    value: 'draft',
    label: 'Draft',
    color: '#9E9E9E',
    icon: 'document-text',
  },
  {
    value: 'finalized',
    label: 'Finalized',
    color: '#2196F3',
    icon: 'checkmark-circle',
  },
  {
    value: 'sent',
    label: 'Sent',
    color: '#FF9800',
    icon: 'send',
  },
  {
    value: 'approved',
    label: 'Approved',
    color: '#4CAF50',
    icon: 'checkmark-done',
  },
  {
    value: 'rejected',
    label: 'Rejected',
    color: '#F44336',
    icon: 'close-circle',
  },
];

export const CLIENT_STATUSES = [
  {
    value: 'prospect',
    label: 'Prospect',
    color: '#FF9800',
    icon: 'eye',
  },
  {
    value: 'active',
    label: 'Active',
    color: '#4CAF50',
    icon: 'checkmark-circle',
  },
  {
    value: 'inactive',
    label: 'Inactive',
    color: '#9E9E9E',
    icon: 'pause-circle',
  },
];

export const ACTIVITY_TYPES = [
  {
    type: 'estimate_created',
    label: 'Estimate Created',
    icon: 'document-text',
    color: '#2196F3',
  },
  {
    type: 'estimate_updated',
    label: 'Estimate Updated',
    icon: 'create',
    color: '#FF9800',
  },
  {
    type: 'client_added',
    label: 'Client Added',
    icon: 'person-add',
    color: '#4CAF50',
  },
  {
    type: 'template_created',
    label: 'Template Created',
    icon: 'library',
    color: '#9C27B0',
  },
];

export const CURRENCIES = [
  { code: 'USD', symbol: '$', name: 'US Dollar' },
  { code: 'EUR', symbol: '€', name: 'Euro' },
  { code: 'GBP', symbol: '£', name: 'British Pound' },
  { code: 'CAD', symbol: 'C$', name: 'Canadian Dollar' },
  { code: 'AUD', symbol: 'A$', name: 'Australian Dollar' },
];

export const DEFAULT_TAX_RATES = [
  { label: 'No Tax', value: 0 },
  { label: '5%', value: 0.05 },
  { label: '7%', value: 0.07 },
  { label: '8.25%', value: 0.0825 },
  { label: '10%', value: 0.10 },
  { label: '13%', value: 0.13 },
  { label: '15%', value: 0.15 },
];
