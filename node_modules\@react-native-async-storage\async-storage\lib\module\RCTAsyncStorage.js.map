{"version": 3, "names": ["NativeModules", "TurboModuleRegistry", "shouldFallbackToLegacyNativeModule", "RCTAsyncStorage", "get"], "sourceRoot": "../../src", "sources": ["RCTAsyncStorage.ts"], "mappings": ";;AAAA,SAASA,aAAa,EAAEC,mBAAmB,QAAQ,cAAc;AACjE,SAASC,kCAAkC,QAAQ,sCAAsC;;AAEzF;AACA;AACA;AACA,IAAIC,eAAe,GAAGF,mBAAmB,GACrCA,mBAAmB,CAACG,GAAG,CAAC,sBAAsB,CAAC;AAAI;AACnDH,mBAAmB,CAACG,GAAG,CAAC,0BAA0B,CAAC,IACnDH,mBAAmB,CAACG,GAAG,CAAC,iBAAiB,CAAC,GAC1CJ,aAAa,CAAC,sBAAsB,CAAC;AAAI;AACzCA,aAAa,CAAC,0BAA0B,CAAC,IACzCA,aAAa,CAAC,iBAAiB,CAAC;AAEpC,IAAI,CAACG,eAAe,IAAID,kCAAkC,CAAC,CAAC,EAAE;EAC5D,IAAID,mBAAmB,EAAE;IACvBE,eAAe,GACbF,mBAAmB,CAACG,GAAG,CAAC,sBAAsB,CAAC,IAC/CH,mBAAmB,CAACG,GAAG,CAAC,mBAAmB,CAAC;EAChD,CAAC,MAAM;IACLD,eAAe,GACbH,aAAa,CAAC,sBAAsB,CAAC,IACrCA,aAAa,CAAC,mBAAmB,CAAC;EACtC;AACF;AAEA,eAAeG,eAAe", "ignoreList": []}