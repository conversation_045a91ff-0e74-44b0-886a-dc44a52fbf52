// components/forms/EstimateStep1.tsx

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';

import { useTheme } from '@/hooks/useTheme';
import { Client } from '@/types/models';
import { Spacing, FontSizes, FontWeights } from '@/constants/Colors';

import Input from '@/components/ui/Input';
import { EstimateFormData } from './EstimateWizard';

interface EstimateStep1Props {
  formData: EstimateFormData;
  updateFormData: (updates: Partial<EstimateFormData>) => void;
  clients: Client[];
}

export default function EstimateStep1({ formData, updateFormData, clients }: EstimateStep1Props) {
  const { colors } = useTheme();

  const styles = StyleSheet.create({
    container: {
      padding: Spacing.lg,
    },
    section: {
      marginBottom: Spacing.xl,
    },
    sectionTitle: {
      fontSize: FontSizes.lg,
      fontWeight: FontWeights.semibold,
      color: colors.text,
      marginBottom: Spacing.md,
    },
    clientSelector: {
      marginBottom: Spacing.md,
    },
    clientOption: {
      backgroundColor: colors.surface,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: 8,
      padding: Spacing.md,
      marginBottom: Spacing.sm,
    },
    clientOptionSelected: {
      backgroundColor: colors.primary,
      borderColor: colors.primary,
    },
    clientName: {
      fontSize: FontSizes.md,
      fontWeight: FontWeights.medium,
      color: colors.text,
      marginBottom: Spacing.xs,
    },
    clientNameSelected: {
      color: '#FFFFFF',
    },
    clientEmail: {
      fontSize: FontSizes.sm,
      color: colors.textSecondary,
    },
    clientEmailSelected: {
      color: '#FFFFFF',
      opacity: 0.8,
    },
    dateContainer: {
      flexDirection: 'row',
      gap: Spacing.md,
    },
    dateInput: {
      flex: 1,
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Select Client</Text>
        <View style={styles.clientSelector}>
          {clients.map((client) => (
            <TouchableOpacity
              key={client.id}
              style={[
                styles.clientOption,
                formData.clientId === client.id && styles.clientOptionSelected,
              ]}
              onPress={() => updateFormData({ clientId: client.id })}
            >
              <Text
                style={[
                  styles.clientName,
                  formData.clientId === client.id && styles.clientNameSelected,
                ]}
              >
                {client.name}
              </Text>
              <Text
                style={[
                  styles.clientEmail,
                  formData.clientId === client.id && styles.clientEmailSelected,
                ]}
              >
                {client.email}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Project Information</Text>
        
        <Input
          label="Project Name"
          value={formData.projectName}
          onChangeText={(text) => updateFormData({ projectName: text })}
          placeholder="Enter project name"
          required
        />

        <Input
          label="Project Description"
          value={formData.projectDescription}
          onChangeText={(text) => updateFormData({ projectDescription: text })}
          placeholder="Describe the project scope and requirements"
          multiline
          numberOfLines={4}
          required
        />

        <View style={styles.dateContainer}>
          <Input
            label="Start Date"
            value={formData.startDate}
            onChangeText={(text) => updateFormData({ startDate: text })}
            placeholder="YYYY-MM-DD"
            containerStyle={styles.dateInput}
            required
          />
          
          <Input
            label="End Date"
            value={formData.endDate}
            onChangeText={(text) => updateFormData({ endDate: text })}
            placeholder="YYYY-MM-DD"
            containerStyle={styles.dateInput}
            required
          />
        </View>
      </View>
    </View>
  );
}
