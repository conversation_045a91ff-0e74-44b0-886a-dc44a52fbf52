// components/forms/EstimateStep2.tsx

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
} from 'react-native';

import { useTheme } from '@/hooks/useTheme';
import { EstimateItem } from '@/types/models';
import { calculateItemTotal } from '@/utils/calculations';
import { TRADE_CATEGORIES } from '@/constants/categories';
import { Spacing, FontSizes, FontWeights } from '@/constants/Colors';

import Input from '@/components/ui/Input';
import Button from '@/components/ui/Button';
import Modal from '@/components/ui/Modal';
import { EstimateFormData } from './EstimateWizard';

interface EstimateStep2Props {
  formData: EstimateFormData;
  updateFormData: (updates: Partial<EstimateFormData>) => void;
}

interface ItemFormData {
  description: string;
  quantity: string;
  unitPrice: string;
  category: string;
  notes: string;
}

export default function EstimateStep2({ formData, updateFormData }: EstimateStep2Props) {
  const { colors } = useTheme();
  const [showItemModal, setShowItemModal] = useState(false);
  const [editingItem, setEditingItem] = useState<EstimateItem | null>(null);
  const [itemForm, setItemForm] = useState<ItemFormData>({
    description: '',
    quantity: '1',
    unitPrice: '0',
    category: 'general',
    notes: '',
  });

  const resetItemForm = () => {
    setItemForm({
      description: '',
      quantity: '1',
      unitPrice: '0',
      category: 'general',
      notes: '',
    });
    setEditingItem(null);
  };

  const handleAddItem = () => {
    resetItemForm();
    setShowItemModal(true);
  };

  const handleEditItem = (item: EstimateItem) => {
    setItemForm({
      description: item.description,
      quantity: item.quantity.toString(),
      unitPrice: item.unitPrice.toString(),
      category: item.category || 'general',
      notes: item.notes || '',
    });
    setEditingItem(item);
    setShowItemModal(true);
  };

  const handleDeleteItem = (itemId: string) => {
    Alert.alert(
      'Delete Item',
      'Are you sure you want to delete this item?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            const updatedItems = formData.items.filter(item => item.id !== itemId);
            updateFormData({ items: updatedItems });
          },
        },
      ]
    );
  };

  const handleSaveItem = () => {
    if (!itemForm.description.trim()) {
      Alert.alert('Validation Error', 'Please enter a description for the item.');
      return;
    }

    const quantity = parseFloat(itemForm.quantity) || 0;
    const unitPrice = parseFloat(itemForm.unitPrice) || 0;

    if (quantity <= 0) {
      Alert.alert('Validation Error', 'Quantity must be greater than 0.');
      return;
    }

    if (unitPrice < 0) {
      Alert.alert('Validation Error', 'Unit price cannot be negative.');
      return;
    }

    const total = calculateItemTotal(quantity, unitPrice);

    const newItem: EstimateItem = {
      id: editingItem?.id || Date.now().toString(),
      description: itemForm.description.trim(),
      quantity,
      unitPrice,
      total,
      category: itemForm.category,
      notes: itemForm.notes.trim() || undefined,
    };

    let updatedItems;
    if (editingItem) {
      updatedItems = formData.items.map(item =>
        item.id === editingItem.id ? newItem : item
      );
    } else {
      updatedItems = [...formData.items, newItem];
    }

    updateFormData({ items: updatedItems });
    setShowItemModal(false);
    resetItemForm();
  };

  const getCategoryName = (categoryId: string) => {
    const category = TRADE_CATEGORIES.find(c => c.id === categoryId);
    return category?.name || 'General';
  };

  const getTotalAmount = () => {
    return formData.items.reduce((sum, item) => sum + item.total, 0);
  };

  const styles = StyleSheet.create({
    container: {
      padding: Spacing.lg,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: Spacing.lg,
    },
    title: {
      fontSize: FontSizes.lg,
      fontWeight: FontWeights.semibold,
      color: colors.text,
    },
    itemsList: {
      marginBottom: Spacing.lg,
    },
    itemCard: {
      backgroundColor: colors.surface,
      padding: Spacing.md,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: colors.border,
      marginBottom: Spacing.sm,
    },
    itemHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: Spacing.sm,
    },
    itemDescription: {
      fontSize: FontSizes.md,
      fontWeight: FontWeights.medium,
      color: colors.text,
      flex: 1,
      marginRight: Spacing.sm,
    },
    itemActions: {
      flexDirection: 'row',
      gap: Spacing.sm,
    },
    actionButton: {
      paddingHorizontal: Spacing.sm,
      paddingVertical: Spacing.xs,
      borderRadius: 4,
    },
    editButton: {
      backgroundColor: colors.primary,
    },
    deleteButton: {
      backgroundColor: colors.error,
    },
    actionButtonText: {
      fontSize: FontSizes.xs,
      fontWeight: FontWeights.medium,
      color: '#FFFFFF',
    },
    itemDetails: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    itemInfo: {
      flex: 1,
    },
    itemQuantity: {
      fontSize: FontSizes.sm,
      color: colors.textSecondary,
    },
    itemPrice: {
      fontSize: FontSizes.sm,
      color: colors.textSecondary,
    },
    itemTotal: {
      fontSize: FontSizes.lg,
      fontWeight: FontWeights.semibold,
      color: colors.primary,
    },
    totalSection: {
      backgroundColor: colors.surface,
      padding: Spacing.lg,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: colors.border,
    },
    totalLabel: {
      fontSize: FontSizes.md,
      color: colors.textSecondary,
      marginBottom: Spacing.xs,
    },
    totalAmount: {
      fontSize: FontSizes.xl,
      fontWeight: FontWeights.bold,
      color: colors.primary,
    },
    emptyState: {
      padding: Spacing.xl,
      alignItems: 'center',
    },
    emptyText: {
      fontSize: FontSizes.md,
      color: colors.textSecondary,
      textAlign: 'center',
      marginBottom: Spacing.lg,
    },
    modalContent: {
      padding: Spacing.lg,
    },
    categorySelector: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: Spacing.sm,
      marginBottom: Spacing.md,
    },
    categoryOption: {
      paddingHorizontal: Spacing.md,
      paddingVertical: Spacing.sm,
      borderRadius: 20,
      backgroundColor: colors.surface,
      borderWidth: 1,
      borderColor: colors.border,
    },
    categoryOptionSelected: {
      backgroundColor: colors.primary,
      borderColor: colors.primary,
    },
    categoryText: {
      fontSize: FontSizes.sm,
      color: colors.text,
    },
    categoryTextSelected: {
      color: '#FFFFFF',
    },
    quantityPriceRow: {
      flexDirection: 'row',
      gap: Spacing.md,
    },
    quantityInput: {
      flex: 1,
    },
    priceInput: {
      flex: 2,
    },
    modalButtons: {
      flexDirection: 'row',
      gap: Spacing.md,
      marginTop: Spacing.lg,
    },
    modalButton: {
      flex: 1,
    },
  });

  const renderItem = ({ item }: { item: EstimateItem }) => (
    <View style={styles.itemCard}>
      <View style={styles.itemHeader}>
        <Text style={styles.itemDescription}>{item.description}</Text>
        <View style={styles.itemActions}>
          <TouchableOpacity
            style={[styles.actionButton, styles.editButton]}
            onPress={() => handleEditItem(item)}
          >
            <Text style={styles.actionButtonText}>Edit</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.actionButton, styles.deleteButton]}
            onPress={() => handleDeleteItem(item.id)}
          >
            <Text style={styles.actionButtonText}>Delete</Text>
          </TouchableOpacity>
        </View>
      </View>
      <View style={styles.itemDetails}>
        <View style={styles.itemInfo}>
          <Text style={styles.itemQuantity}>
            Qty: {item.quantity} × ${item.unitPrice.toFixed(2)}
          </Text>
          <Text style={styles.itemPrice}>
            Category: {getCategoryName(item.category || 'general')}
          </Text>
        </View>
        <Text style={styles.itemTotal}>${item.total.toFixed(2)}</Text>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Estimate Items</Text>
        <Button
          title="Add Item"
          onPress={handleAddItem}
          size="small"
        />
      </View>

      {formData.items.length > 0 ? (
        <View style={styles.itemsList}>
          <FlatList
            data={formData.items}
            renderItem={renderItem}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
          />
        </View>
      ) : (
        <View style={styles.emptyState}>
          <Text style={styles.emptyText}>
            No items added yet.{'\n'}
            Click "Add Item" to start building your estimate.
          </Text>
        </View>
      )}

      <View style={styles.totalSection}>
        <Text style={styles.totalLabel}>Subtotal</Text>
        <Text style={styles.totalAmount}>${getTotalAmount().toFixed(2)}</Text>
      </View>

      <Modal
        visible={showItemModal}
        onClose={() => setShowItemModal(false)}
        title={editingItem ? 'Edit Item' : 'Add New Item'}
        size="large"
      >
        <View style={styles.modalContent}>
          <Input
            label="Description"
            value={itemForm.description}
            onChangeText={(text) => setItemForm(prev => ({ ...prev, description: text }))}
            placeholder="Enter item description"
            required
          />

          <Text style={styles.title}>Category</Text>
          <View style={styles.categorySelector}>
            {TRADE_CATEGORIES.slice(0, 6).map((category) => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryOption,
                  itemForm.category === category.id && styles.categoryOptionSelected,
                ]}
                onPress={() => setItemForm(prev => ({ ...prev, category: category.id }))}
              >
                <Text
                  style={[
                    styles.categoryText,
                    itemForm.category === category.id && styles.categoryTextSelected,
                  ]}
                >
                  {category.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          <View style={styles.quantityPriceRow}>
            <Input
              label="Quantity"
              value={itemForm.quantity}
              onChangeText={(text) => setItemForm(prev => ({ ...prev, quantity: text }))}
              placeholder="1"
              keyboardType="numeric"
              containerStyle={styles.quantityInput}
              required
            />
            <Input
              label="Unit Price ($)"
              value={itemForm.unitPrice}
              onChangeText={(text) => setItemForm(prev => ({ ...prev, unitPrice: text }))}
              placeholder="0.00"
              keyboardType="numeric"
              containerStyle={styles.priceInput}
              required
            />
          </View>

          <Input
            label="Notes (Optional)"
            value={itemForm.notes}
            onChangeText={(text) => setItemForm(prev => ({ ...prev, notes: text }))}
            placeholder="Additional notes about this item"
            multiline
            numberOfLines={2}
          />

          <View style={styles.modalButtons}>
            <Button
              title="Cancel"
              onPress={() => setShowItemModal(false)}
              variant="outline"
              style={styles.modalButton}
            />
            <Button
              title={editingItem ? 'Update Item' : 'Add Item'}
              onPress={handleSaveItem}
              style={styles.modalButton}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
}
